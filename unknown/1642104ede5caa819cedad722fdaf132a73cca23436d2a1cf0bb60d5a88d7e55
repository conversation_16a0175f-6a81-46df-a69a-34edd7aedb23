import React, { useEffect, useRef, useState } from 'react'
import '../../../../src/App.css'
import { Typography } from '@mui/material'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import 'swiper/css/navigation'
import { getLoginToken } from '../../../utils/storageUtils'
import { useUserStore } from '../../../store/useUserSlice'
import { toast } from 'react-hot-toast'
import { usePortalStore } from '../../../store/userPortalSlice'
import { useFavToggleMutation } from '../../../reactQuery/casinoQuery'
import GameSlider from '../../GameSlider/GameSlider'
import GameSliderSkeleton from '../../GameSlider/GameSliderSkeleton'
import Signup from '../../../components/Modal/Signup'
import {
  useCoinStore,
  useSelectedProviderStore,
  useGamesStore
} from '../../../store/store'
import { useNavigate, useLocation } from 'react-router-dom'
import GameList from './GamesList'
import GamesListSkeleton from './GamesListSkeleton'
import ScrollToTop from '../../../components/ScrollToTop'
import { CasinoQuery } from '../../../reactQuery'
import { PlayerRoutes } from '../../../routes'
import TagManager from 'react-gtm-module'
import { SubCategoryConstants } from '../../../components/SideBar/constants'
import { dynamicMerge, updateFavoriteGames } from '../../../utils/helpers'
import { setCookie } from '../../../utils/cookiesCollection'
import { usePragmaticJackpotStore } from '../../../store/usePragmaticJackpot'

const GamesGrid = () => {
  const {
    gameData,
    setGameData,
    gameDataCount,
    setGameDataCount,
    setPageNo,
    limit,
    pageNo,
    setSubCategoryName,
    setFeaturedSubcategory,
    setSubCategories,
    selectedSubCategoryId,
    selectedProvider,
    setGameDataLoading,
    gameDataLoading,
    featuredSubcategory,
    favorites,
    setFavorites,
    setFavoriteIds,
    recentGamesList,
    setRecentGamesList
  } = useGamesStore((state) => state)

  const subCategoryName = useGamesStore((state) => state.subCategoryName)
  const subCategories = useGamesStore((state) => state.subCategories)
  const selectedSubCat = useGamesStore((state) => state.selectedSubCat)
  const { setPragmaticJackpotSc, setPragmaticJackpotGc } = usePragmaticJackpotStore()

  const featuredSubcategoryNames = featuredSubcategory?.map((x) => x?.name)

  const previousSubCategoryName = useRef(subCategoryName)

  const { state } = useLocation()
  const coinType = useCoinStore((state) => {
    return state.coinType
  })
  const inputRef = useRef(null)
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)
  const auth = useUserStore((state) => state)
  const userDetails = useUserStore((state) => state.userDetails)
  const sliderRef = useRef(null)
  const selectedProviderId = useSelectedProviderStore((state) => state.selectedProviderId)
  const [isRefreshingFavorites, setIsRefreshingFavorites] = useState(false)

  useEffect(() => {
    if (state?.showInputFocus) inputRef.current.focus()
  }, [state])

  useEffect(() => {
    const elem = sliderRef.current
    if (!elem) return

    let isDragging = false
    let startX
    let scrollLeft

    const dragStart = (e) => {
      isDragging = true
      startX = e.pageX - elem.offsetLeft
      scrollLeft = elem.scrollLeft
    }

    const dragEnd = () => {
      isDragging = false
    }

    const drag = (e) => {
      if (!isDragging) return
      e.preventDefault()
      const x = e.pageX - elem.offsetLeft
      const walk = (x - startX) * 2 // Scroll-fast factor
      elem.scrollLeft = scrollLeft - walk
    }

    const handleTouchStart = (e) => {
      const touch = e.touches[0]
      dragStart({ pageX: touch.pageX })
    }

    const handleTouchMove = (e) => {
      const touch = e.touches[0]
      drag({ pageX: touch.pageX, preventDefault: () => e.preventDefault() })
    }

    const handleTouchEnd = dragEnd
    elem.addEventListener('pointerdown', dragStart)
    window.addEventListener('pointerup', dragEnd)
    window.addEventListener('pointermove', drag)
    elem.addEventListener('touchstart', handleTouchStart)
    elem.addEventListener('touchmove', handleTouchMove)
    elem.addEventListener('touchend', handleTouchEnd)

    return () => {
      elem.removeEventListener('pointerdown', dragStart)
      window.removeEventListener('pointerup', dragEnd)
      window.removeEventListener('pointermove', drag)
      elem.removeEventListener('touchstart', handleTouchStart)
      elem.removeEventListener('touchmove', handleTouchMove)
      elem.removeEventListener('touchend', handleTouchEnd)
    }
  }, [])

  const handlePlayNow = (masterCasinoGameId, name, gameType) => {
    if (!!getLoginToken() || auth.isAuthenticate) {
      if (coinType === 'SC' && userDetails?.userWallet?.totalScCoin > 0) {
        navigate(`/game-play/${masterCasinoGameId}`, { state: { name, gameType } })
      } else if (coinType === 'GC' && userDetails?.userWallet?.gcCoin > 0) {
        navigate(`/game-play/${masterCasinoGameId}`, { state: { name, gameType } })
      } else {
        toast.error('Please make a purchase!')
        navigate(PlayerRoutes.Store)
      }
    } else {
      portalStore.openPortal(() => <Signup />, 'signupModal')
    }
  }

  const mutationFavToggle = useFavToggleMutation({
    onSuccess: (res, variables) => {
      const newFavoriteState = variables?.request

      setFavorites(prevFavorites => ({
        ...prevFavorites,
        [variables.gameId]: newFavoriteState
      }))

      setFavoriteIds(prevFavoriteIds => ({
        ...prevFavoriteIds,
        [variables.gameId]: newFavoriteState
      }))

      if (subCategoryName === 'Lobby' && selectedProvider) {
        subcategoryListMutation.mutate({ masterCasinoProviderId: selectedProviderId })
      } else {
        subcategoryListMutation.mutate()
      }

      // If we're in the favorite games section, refresh the list
      if (selectedSubCat === SubCategoryConstants.FAVORITE_GAMES) {
        setIsRefreshingFavorites(true)
        setPageNo(1)
        const filterData = { limit: limit, page: 1 }
        favGameListMutation.mutate(filterData)
      }

      toast.success(variables?.request ? 'Added to Favorites' : 'Removed from Favorites')
    },
    onError: (error) => {
      if (error?.response?.data?.errors.length > 0) {
        const { errors } = error.response.data
        errors.forEach((error) => {
          console.log(error)
        })
      }
    }
  })

  const toggleFavorite = (gameId, isFav) => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'favorite_game',
        game_Id: gameId,
        favorite_status: !isFav,
        user_id: userDetails?.userId,
        email: userDetails?.email
      }
    })
    mutationFavToggle.mutate({ request: !isFav, gameId })
  }

  const successToggler = (data) => {
    if (subCategoryName === SubCategoryConstants.ALL_GAMES) {
      const newData = data?.data?.data?.rows
      if (selectedProviderId !== '') {
        setGameData(data?.data?.data?.rows)
        setGameDataCount(data?.data?.data?.count)
        setGameDataLoading(false)
        return
      }
      const favoriteIds = Object.keys(favorites).filter(id => favorites[id]).map(id => parseInt(id))
      const newArray = updateFavoriteGames(favoriteIds, gameData)
      const updatedGameData = dynamicMerge(newArray || [], newData || [], 'masterCasinoGameId')
      setGameData(updatedGameData)
      setGameDataCount(data?.data?.data?.count)
      setGameDataLoading(false)
    } else if (subCategoryName === 'Lobby') {
      const newData = data?.data?.data
      if (selectedProviderId !== '') {
        setFeaturedSubcategory(newData)
        setSubCategories(newData)
        setGameDataLoading(false)
        return
      }
      const updatedFeaturedSubcategory = dynamicMerge(
        featuredSubcategory || [],
        newData || [],
        'masterGameSubCategoryId',
        'subCategoryGames',
        'masterCasinoGameId'
      )
      setFeaturedSubcategory(updatedFeaturedSubcategory)
      setSubCategories(updatedFeaturedSubcategory)
      setPragmaticJackpotGc(subCategories?.pragmaticActiveJackpotDetails?.gcType)
      setPragmaticJackpotSc(subCategories?.pragmaticActiveJackpotDetails?.scType)
    } else {
      const newData1 = data?.data?.data[0]?.subCategoryGames
      const favoriteIds = Object.keys(favorites).filter(id => favorites[id]).map(id => parseInt(id))
      const newArray = updateFavoriteGames(favoriteIds, gameData)
      const updatedGameData = dynamicMerge(newArray || [], newData1 || [], 'masterCasinoGameId')
      setGameData(updatedGameData)
      setGameDataCount(data?.data?.data[0]?.totalGames)
      setGameDataLoading(false)
    }
  }

  /* error response of subcategoryListMutation */
  const errorToggler = () => {
    setGameDataLoading(false)
  }

  /* subcategoryListMutation api hit  */
  const subcategoryListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })

  const recentSuccessToggler = (data) => {
    const newData = data?.data?.data?.rows
    if (pageNo >= 2) {
      const newArray = recentGamesList
      const updatedGameData = dynamicMerge(newArray || [], newData || [], 'masterCasinoGameId')
      setGameData(updatedGameData)
      setRecentGamesList(updatedGameData)
    } else {
      setGameData(newData)
      setRecentGamesList(newData)
    }
    setGameDataCount(data?.data?.data?.count)
    setGameDataLoading(false)
  }

  const recentErrorToggler = () => {
    setGameDataLoading(false)
  }

  const recentGameListMutation = CasinoQuery.useRecentPlayListMutation({
    successToggler: recentSuccessToggler,
    errorToggler: recentErrorToggler
  })

  const favSuccessToggler = (data1) => {
    const newData = data1?.data?.data?.rows
    if (isRefreshingFavorites || pageNo === 1) {
      // Replace data when refreshing after favorite toggle or on first page
      setGameData(newData)
      setIsRefreshingFavorites(false)
    } else {
      // Append data for pagination
      const updatedGameData = dynamicMerge(gameData || [], newData || [], 'masterCasinoGameId')
      setGameData(updatedGameData)
    }
    setGameDataCount(data1?.data?.data?.count)
    setGameDataLoading(false)
  }

  const favErrorToggler = (error) => {
    console.log(error)
    setGameDataLoading(false)
  }

  const favGameListMutation = CasinoQuery.useFavGameListMutation({
    successToggler: favSuccessToggler,
    errorToggler: favErrorToggler
  })

  const handleLoadMore = () => {
    let filterData = {}
    if (Math.ceil(gameDataCount / limit) > pageNo) {
      setPageNo(pageNo + 1)
      if (
        selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED ||
        selectedSubCat === SubCategoryConstants.FAVORITE_GAMES
      ) {
        filterData = {
          limit: limit,
          page: pageNo + 1
        }
      } else {
        filterData = {
          subCategoryId: subCategoryName !== SubCategoryConstants.ALL_GAMES ? selectedSubCategoryId : 0,
          limit: limit,
          page: pageNo + 1
        }
      }
      if (selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED) {
        recentGameListMutation.mutate(filterData)
      } else if (selectedSubCat === SubCategoryConstants.FAVORITE_GAMES) {
        favGameListMutation.mutate(filterData)
      } else {
        subcategoryListMutation.mutate(filterData)
      }
    } else return undefined
  }

  useEffect(() => {
    if (!subCategories) return

    const initialFavorites = {}

    subCategories.forEach((subcategory) => {
      subcategory?.subCategoryGames?.forEach((game) => {
        if (game.FavoriteGames === true && game.masterCasinoGameId != null) {
          initialFavorites[game.masterCasinoGameId?.toString()] = true
        }
      })
    })

    // Merge with existing favorites instead of replacing them
    setFavorites(prevFavorites => ({
      ...prevFavorites,
      ...initialFavorites
    }))
  }, [subCategories])

  useEffect(() => {
    if (
      selectedSubCat !== SubCategoryConstants.FAVORITE_GAMES &&
      selectedSubCat !== SubCategoryConstants.RECENTLY_PLAYED
    ) {
      if (subCategoryName === SubCategoryConstants.ALL_GAMES) {
        if (selectedProviderId !== '') {
          subcategoryListMutation.mutate({ masterCasinoProviderId: selectedProviderId, subCategoryId: 0, limit: limit, page: pageNo })
          return
        }
        subcategoryListMutation.mutate({ subCategoryId: 0, limit: limit, page: pageNo })
      } else if (featuredSubcategoryNames.includes(selectedSubCat)) {
        const subId = featuredSubcategory?.find((x) => x?.name === selectedSubCat)?.masterGameSubCategoryId
        subcategoryListMutation.mutate({ subCategoryId: subId, limit: limit, page: pageNo })
      } else if (subCategoryName === 'Lobby') {
        if (selectedProviderId !== '') {
          subcategoryListMutation.mutate({ masterCasinoProviderId: selectedProviderId })
        } else {
          subcategoryListMutation.mutate()
        }
      } else {
        setCookie('onloadGameApi', false)
      }
    }
  }, [subCategoryName, selectedProviderId])

  useEffect(() => {
    let filterData = {}
    setGameData([])
    setPageNo(1)
    if (previousSubCategoryName.current !== selectedSubCat) {
      filterData = { limit: limit, page: 1 }
    } else {
      filterData = { limit: limit, page: pageNo }
    }
    if (selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED) {
      recentGameListMutation.mutate(filterData)
    } else if (selectedSubCat === SubCategoryConstants.FAVORITE_GAMES) {
      favGameListMutation.mutate(filterData)
    }
  }, [selectedSubCat])

  return (
    <>
      <ScrollToTop />
      {subCategoryName === 'Lobby' && featuredSubcategory.length === 0
        ? (
          <div
            style={{
              minHeight: '600px', // Reserve space equivalent to multiple game sliders
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '3rem 1rem',
              margin: '2rem 0'
            }}
          >
            <div
              style={{
                textAlign: 'center',
                padding: '3rem 2rem',
                backgroundColor: 'rgba(255, 255, 255, 0.05)',
                borderRadius: '16px',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                maxWidth: '500px',
                width: '100%',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
              }}
            >
              <Typography
                variant='h5'
                sx={{
                  textAlign: 'center',
                  color: 'rgba(255, 255, 255, 0.8)',
                  marginBottom: '1.5rem',
                  fontWeight: 500,
                  fontSize: { xs: '1.25rem', md: '1.5rem' }
                }}
              >
                No Games Available
              </Typography>
              <Typography
                variant='body1'
                sx={{
                  textAlign: 'center',
                  color: 'rgba(255, 255, 255, 0.6)',
                  lineHeight: 1.6,
                  fontSize: { xs: '0.95rem', md: '1.1rem' }
                }}
              >
                Games will appear here once they're loaded
              </Typography>
            </div>
          </div>)
        : (
          <></>)}
      {subCategoryName === 'Lobby'
        ? (
          <>
            {gameDataLoading && featuredSubcategory.length === 0
              ? (
                // Show skeleton loaders when loading and no data yet
                <>
                  <GameSliderSkeleton title='Featured Games' />
                  <GameSliderSkeleton title='Popular Games' />
                  <GameSliderSkeleton title='New Games' />
                </>)
              : (
                  featuredSubcategory?.map((subCategory, index) => {
                    if (index === 0 && subCategory.subCategoryGames.length > 0) {
                      return (
                        <React.Fragment key={`gameSlider-${index}`}>
                          <GameSlider
                            index={index}
                            subCategory={subCategory}
                            handlePlayNow={handlePlayNow}
                            toggleFavorite={toggleFavorite}
                            keySubCategory={setSubCategoryName}
                          />
                        </React.Fragment>
                      )
                    }
                    if (subCategory.subCategoryGames.length > 0 && subCategory.isFeatured) {
                      return (
                        <GameSlider
                          key={`gameSlider-${index}`}
                          index={index}
                          subCategory={subCategory}
                          handlePlayNow={handlePlayNow}
                          toggleFavorite={toggleFavorite}
                          keySubCategory={setSubCategoryName}
                        />
                      )
                    }
                    return null
                  })
                )}
          </>)
        : (
            gameDataLoading && gameData.length === 0
              ? (
                <GamesListSkeleton count={12} />
                )
              : (
                <GameList
                  handlePlayNow={handlePlayNow}
                  handleLoadMore={handleLoadMore}
                />
                )
          )}
    </>
  )
}

export default GamesGrid
