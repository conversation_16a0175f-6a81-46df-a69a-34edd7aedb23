import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  LobbyFaq: {
    '& .MuiAccordion-root': {
      backgroundColor: 'transparent !important',
      border: `1px solid ${theme.colors.accordianBorder}`,
      borderRadius: `${theme.spacing(1.25)} !important`,
      margin: '0 !important',
      marginBottom: '1rem !important',
      '&.Mui-expanded': {
        borderColor: theme.colors.YellowishOrange
      },

      '& .MuiAccordionSummary-root': {
        padding: theme.spacing(1.1, 1.25),
        position: 'relative',
        backgroundColor: 'transparent !important',
        '& .MuiAccordionSummary-content': {
          margin: '0',
          '& .MuiTypography-root': {
            fontSize: theme.spacing(1.375),
            marginBottom: '0',
            fontWeight: theme.typography.fontWeightExtraBold,
            color: theme.colors.textWhite,
            [theme.breakpoints.down('md')]: {
              fontSize: theme.spacing(1.125),
              width: '90%'
            }
          },
          '&.Mui-expanded': {
            '& .MuiTypography-root': {
              color: theme.colors.YellowishOrange
            }
          }
        },
        '& .MuiAccordionSummary-expandIconWrapper': {
          position: 'absolute',
          right: theme.spacing(2),
          color: theme.colors.YellowishOrange,
          '& svg': {
            width: theme.spacing(2),
            height: theme.spacing(2)
          }
        }
      },
      '& .MuiAccordionDetails-root': {
        padding: theme.spacing(0.5, 1),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1)
        },
        '& .MuiTypography-root': {
          color: theme.colors.textWhite,
          fontSize: theme.spacing(1.25),
          textAlign: 'left',
          lineHeight: theme.spacing(1.75),
          fontWeight: theme.typography.fontWeightMedium,
          marginBottom: theme.spacing(1),
          '& a': {
            color: theme.colors.YellowishOrange,
            textDecoration: 'none',
            padding: theme.spacing(0, 0.2)
          }
        }
      }
    }
  },
  H1Text: {
    fontSize: '2.75rem',
    fontWeight: '700',
    textAlign: 'center',
    marginTop: '5rem',
    [theme.breakpoints.down('md')]: {
      fontSize: '1.75rem',
      marginTop: '3rem'
    },
    '& span': {
      color: '#FDB72E'
    }
  },
  H2Text: {
    fontSize: '2.75rem',
    textAlign: 'center',
    fontWeight: '700',
    marginBottom: '2rem',
    [theme.breakpoints.down('md')]: {
      fontSize: '1.75rem',
      marginBottom: '1rem'
    },
    '& span': {
      color: '#FDB72E'
    }
  }
}))
