import React, { useEffect } from 'react'
import useAuthStore from './store/useAuthStore'
import Lobby from './pages/Lobby/Lobby'
import { useNavigate } from 'react-router-dom'
import Loader from './components/Loader'

const LandingWrapper = () => {
  const navigate = useNavigate()
  const { pathCookieCheck } = useAuthStore()

  // Use useEffect to handle navigation after render
  useEffect(() => {
    if (!pathCookieCheck) {
      // Use immediate navigation with replace to reduce delay
      navigate('/online-social-casino-games', { replace: true })
    }
  }, [pathCookieCheck, navigate])

  // If pathCookieCheck is true, render Lobby
  if (pathCookieCheck) {
    return <Lobby />
  }

  // Show loader instead of null to prevent black screen
  // This will only show for a few milliseconds during navigation
  return <Loader />
}

export default LandingWrapper
