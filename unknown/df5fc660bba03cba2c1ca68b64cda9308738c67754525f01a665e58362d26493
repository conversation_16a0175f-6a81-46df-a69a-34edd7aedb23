import React, { useEffect, useState, lazy, Suspense } from 'react'
import useStyles from './style'
import LandingHeader from './LandingHeader'
import LandingBanner from './LandingBanner'
import { usePortalStore } from '../../store/userPortalSlice'
import { useLocation, useNavigate } from 'react-router-dom'
import { setCookie } from '../../utils/cookiesCollection'
import { useGetProfileMutation } from '../../reactQuery'
import { useUserStore } from '../../store/useUserSlice'
import UserNameModal from '../../components/Modal/Signup/UserNameModal'
import Popup2Fa from '../popup2Fa'
import useIntercom from '../../components/SideBar/hooks/useIntercom'
import Maintenance from '../NotFound/Maintanace'
import JackpotBadge from '../Jackpot/JackpotBadge'
import SeoHead from '../../utils/seoHead'
import CriticalResourcePreloader from '../../components/CriticalResourcePreloader'

// Lazy load components that are below the fold
const SocialSection = lazy(() => import('./SocailSection'))
const ChooseMoneyFactory = lazy(() => import('./WhyChooseMoneyFacrory'))
const ProviderSection = lazy(() => import('./ProviderSection'))
const NewGamesWeek = lazy(() => import('./NewGamesWeek'))
const ProviderCategorySection = lazy(() => import('./ProviderCategorySection'))
const WhyPlayMneyFactory = lazy(() => import('./WhyPlayMoneyFactory'))
const DiscoverSection = lazy(() => import('./DiscoverSection'))
const FeaturesSection = lazy(() => import('./FeaturesSection'))
const UltimateSection = lazy(() => import('./UltimateSection'))
const Testimonials = lazy(() => import('./Testimonaials'))
const ContactSection = lazy(() => import('./ContactSection'))
const LandingFooter = lazy(() => import('./LandingFooter'))

// Optimized loading component for better UX and accessibility
const SectionLoader = () => (
  <div
    style={{
      height: '200px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent'
    }}
    role="status"
    aria-label="Loading content"
  >
    <div
      style={{
        width: '40px',
        height: '40px',
        border: '3px solid #f3f3f3',
        borderTop: '3px solid #3498db',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}
      aria-hidden="true"
    />
    <span className="sr-only">Loading...</span>
  </div>
)

const Landing = () => {
  const { showIntercom } = useIntercom(!(import.meta.env.VITE_NODE_ENV === 'production'), true)
  const portalStore = usePortalStore()
  const classes = useStyles()
  const navigate = useNavigate()
  const user = useUserStore((state) => state)
  const location = useLocation()

  const afterLogin = (res) => {
    if (res?.data?.data?.isEmailVerified) {
      localStorage.setItem('username', res?.data?.data?.username)
      user.setUserDetails(res?.data?.data)
      user.setIsAuthenticate(true)
    }
  }
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      if (res?.data?.data?.authEnable) {
        portalStore.openPortal(() => <Popup2Fa res={res} afterLogin={afterLogin} />, 'valutModal')
        return
      }
      afterLogin(res)
    },
    onError: (error) => {
      if (error && error[0].name === 'UserNameDoesNotExistError') {
        portalStore.openPortal(() => <UserNameModal />, 'loginModal')
      }
    }
  })

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.origin !== import.meta.env.VITE_LANDING_PAGE_URL) {
        return // Ignore messages from unknown sources
      }

      if (event.data && event.data.type === 'accessToken') {
        setCookie('path', '/home', 30)
        if (import.meta.env.VITE_NODE_ENV === 'production') {
          setCookie('accessToken', event.data.token, 1)
        } else if (import.meta.env.VITE_NODE_ENV === 'staging') {
          setCookie('accessToken', event.data.token, 1)
        }
        getProfileMutation.mutate()
        navigate('/')
      }
    }

    // Add the event listener for messages
    window.addEventListener('message', handleMessage, false)

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener('message', handleMessage, false)
    }
  }, [])

  useEffect(() => {
    const sectionId = location.state?.sectionId
    if (sectionId) {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }, [location.state])



  const [socketData, setSocketData] = useState(() => {
    const storedData = localStorage.getItem('socketData')
    return storedData ? JSON.parse(storedData) : null
  })
  const ismaintenancemode = socketData?.isActive || false
  if (ismaintenancemode) {
    return <Maintenance initialMinutes={socketData?.remainingMinutes} />
  }

  return (
    <>
      <CriticalResourcePreloader />
      <SeoHead
        title='Play Social Casino Games Online for Free | The Money Factory'
        description='Enjoy the best social casino games online for free! No deposit needed – just play, have fun, and win virtual prizes at The Money Factory.'
      />
      <main className={classes.landingPageWrap} role="main">
        <header>
          <LandingHeader />
        </header>
        <JackpotBadge />
        <section aria-label="Hero banner">
          <LandingBanner />
        </section>

        <Suspense fallback={<SectionLoader />}>
          <section id='howIt' aria-label="How it works">
            <SocialSection />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Why choose Money Factory">
            <ChooseMoneyFactory />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Game providers">
            <ProviderSection />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="New games weekly">
            <NewGamesWeek />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Provider categories">
            <ProviderCategorySection />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Why play Money Factory">
            <WhyPlayMneyFactory />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Discover section">
            <DiscoverSection />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Features">
            <FeaturesSection />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Ultimate gaming">
            <UltimateSection />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Customer testimonials">
            <Testimonials />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <section aria-label="Contact information">
            <ContactSection />
          </section>
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <footer>
            <LandingFooter />
          </footer>
        </Suspense>
      </main>
      
    </>
  )
}

export default Landing
