import React from 'react'
import { useParams } from 'react-router-dom'

import SocialSlotGames from './components/SeoLandingPages/SlotGames/SocialSlotGames'
import SlingoCasinoGames from './components/SeoLandingPages/SlingoCasino'
import InstantWinCasino from './components/SeoLandingPages/InstantWinCasino'
import ScratchCardGames from './components/SeoLandingPages/ScratchCardGame'
import PopularCasinoGames from './components/SeoLandingPages/PopularCasinoGames'
import LiveDealerCasino from './components/SeoLandingPages/LiveDealerCasino'
import SeoDynamicGamePage from './components/SeoDynamicGamePage'
import CasinoTableGames from './components/SeoLandingPages/CasinoTable'

const seoComponentMap = {
  'social-slot-games': SocialSlotGames,
  'slingo-casino-games': SlingoCasinoGames,
  'casino-table-games': CasinoTableGames, // mapped same component intentionally
  'online-casino-scratch-card-games': ScratchCardGames,
  'instant-win-casino-games': InstantWinCasino,
  'live-dealer-casino-games': LiveDealerCasino,
  'popular-casino-games': PopularCasinoGames
}

const SeoGamePageWrapper = () => {
  const { gameSlug } = useParams()
  // Add a simple test to verify the component is rendering
  if (!gameSlug) {
    return <div>No gameSlug parameter found</div>
  }

  const MatchedComponent = seoComponentMap[gameSlug]

  if (MatchedComponent) {
    return <MatchedComponent />
  }
  return <SeoDynamicGamePage />
}

export default SeoGamePageWrapper
