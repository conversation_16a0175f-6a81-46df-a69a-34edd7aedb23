import React, { Suspense, lazy, useEffect, useState, useMemo, useCallback } from 'react'
import { Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom'
import { usePortalStore, useUserStore } from './store/store'
import Loader from './components/Loader'
import './App.css'
import { Grid } from '@mui/material'
import useStyles from './MainPage.styles'
import {
  deleteAccessTokenCookies,
  deleteCookie,
  getCookie,
  setCookie,
  setReferralSocialCookie,
  setreferralCode
} from './utils/cookiesCollection'
import LayoutWrapper from './LayoutWrapper'
import { useDynamoKey } from './reactQuery'
import { shouldShowHeader, shouldShowSideBarAndFooter } from './withLobbyHeaderAndSidebar'
import appRoutes from './appRoutes'
import { HeaderSkeleton, SidebarSkeleton, FooterSkeleton } from './components/SkeletonLoader'

// Lazy load all layout components for better code splitting
const Footer = lazy(() => import('./components/Footer'))
const Header = lazy(() => import('./components/Header'))
const SideBar = lazy(() => import('./components/SideBar'))

let isUserAuthenticate = localStorage.getItem('username')

export const createRoute = (path, component, onlyWithoutAuth, privateRoute, props, level) => {
  // Optimize store subscriptions - only get what we need
  const isAuthenticate = useUserStore((state) => state.isAuthenticate)

  if (level) {
    return (
      <Route path={path} key={path} element={component} {...props}>
        {getApplicationRoutes(level)}
      </Route>
    )
  } else {
    // for private route
    if (privateRoute) {
      if (!isUserAuthenticate || !isAuthenticate) {
        return <Route path={path} key={path} element={<Navigate replace to='/' />} />
      }
      return <Route path={path} key={path} element={component} {...props} />
    }
    // for only when user not logged in
    if (onlyWithoutAuth && isUserAuthenticate && isAuthenticate) {
      return <Route path={path} key={path} element={<Navigate replace to='/' />} />
    }
    return <Route path={path} key={path} element={component} {...props} />
  }
}

export const getApplicationRoutes = (routes) => {
  return routes.map((route) =>
    createRoute(route.path, route.element, route.onlyWithoutAuth, route.private, route.props, route.level)
  )
}

export const buildRouter = (routesConfig, mapRoutes = getApplicationRoutes) => {
  const applicationRoutes = useMemo(() => mapRoutes(routesConfig), [routesConfig, mapRoutes])
  const location = useLocation()
  const pathname = location.pathname
  const classes = useStyles()
  const navigate = useNavigate()

  // Optimize store subscriptions - only get what we need
  const closePortal = usePortalStore((state) => state.closePortal)
  const isAuthenticate = useUserStore((state) => state.isAuthenticate)
  const isVipApproved = useUserStore((state) => state.isVipApproved)
  const userDetails = useUserStore((state) => state.userDetails)
  const logout = useUserStore((state) => state.logout)

  const [redirectHandled, setRedirectHandled] = useState(false)

  // Memoize socket ribbon data parsing
  const socketRibbonData = useMemo(() => {
    const storedData = getCookie('socketRibbonData')
    return storedData ? JSON.parse(storedData) : null
  }, [])

  // Memoize URL params parsing
  const { paramsData, refferalKey, dynamoKey } = useMemo(() => {
    const params = new URLSearchParams(window.location.search)
    const data = {}
    params.forEach((value, key) => {
      data[key] = value
    })
    return {
      paramsData: data,
      refferalKey: params.get('referralcode'),
      dynamoKey: params.get('d10x_link_id')
    }
  }, [location.search])

  const accessCookie = getCookie('accessToken')

  // Memoize layout visibility calculations
  const showHeader = useMemo(() => shouldShowHeader(pathname), [pathname])
  const showSidebarAndFooter = useMemo(() => shouldShowSideBarAndFooter(pathname), [pathname])

  // Memoize private routes calculation
  const privateRoutes = useMemo(
    () => routesConfig.filter((route) => route.private).map((route) => route.path),
    [routesConfig]
  )
  const dynamoKeySend = useDynamoKey({
    onSuccess: (res) => {
      if (res.data.success) {
        // toast.success('Message Sent') // Remove toast import if not used elsewhere
        deleteCookie('dynamoKey')
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  useEffect(() => {
    if (Object.keys(paramsData).length > 0) {
      const paramsString = JSON.stringify(paramsData)
      document.cookie = `urlParams=${paramsString};`
    }
  }, [paramsData])
  useEffect(() => {
    if (dynamoKey !== null) {
      setCookie('dynamoKey', dynamoKey)
      if (userDetails !== null && dynamoKey !== '') {
        dynamoKeySend.mutate({ d10x_link_id: dynamoKey })
      }
    }
  }, [dynamoKey, userDetails, dynamoKeySend])
  useEffect(() => {
    if (refferalKey !== null) {
      setreferralCode('referralcode', refferalKey)
      setReferralSocialCookie('referralcode', refferalKey, '.themoneyfactory.com')
      deleteCookie('affiliateCode')
      deleteCookie('affiliateId')
      deleteCookie('affiliatePromocode')
    }
  }, [refferalKey])

  // Memoize redirect mappings for better performance
  const redirectMappings = useMemo(() => ({
    '/blogs': '/blog',
    '/games/all-games': '/games',
    '/games/table-games': '/games/casino-table-games',
    '/games/live-dealer': '/games/live-dealer-casino-games',
    '/games/hot-games': '/games',
    '/home': '/online-social-casino-games'
  }), [])

  // Handle simple redirects first (most performant)
  useEffect(() => {
    if (redirectHandled) return

    const redirectTo = redirectMappings[location.pathname]
    if (redirectTo) {
      setRedirectHandled(true)
      navigate(redirectTo, { replace: true })
      return
    }
  }, [location.pathname, redirectHandled, redirectMappings, navigate])

  // Handle complex routing logic in a separate effect
  useEffect(() => {
    if (redirectHandled) return
    setRedirectHandled(true)

    // Memoize expensive path computations
    const pathCookie = getCookie('path')
    const searchParams = location.search
    const fullPath = `${pathname}${searchParams}`
    const cleanedPath = pathname.endsWith('/') ? pathname.slice(0, -1) : pathname

    // Memoize boolean checks for better performance
    const pathChecks = useMemo(() => ({
      paymentCheck: pathname.includes('/in-progress'),
      isForgotPassword: pathname.includes('/forgotPassword'),
      isPublicPage: ['/games', '/blog', '/faq', '/about-us', '/contact-us'].some((path) => pathname.includes(path)),
      isCasinoGamesPage: pathname.includes('/online-social-casino-games'),
      isPrivate: privateRoutes.some((route) => pathname.startsWith(route)),
      isVipRoute: pathname.includes('/vip-player-interests')
    }), [pathname, privateRoutes])

    const params = new URLSearchParams(location.search)
    const vipEmail = params.get('email')

    const { paymentCheck, isForgotPassword, isPublicPage, isCasinoGamesPage, isPrivate, isVipRoute } = pathChecks

    if (isVipRoute) {
      document.cookie = `vipRoute=${location.pathname}${location.search}; path=/;`
      if (userDetails?.email && vipEmail !== userDetails?.email) {
        handleLogoutWithToast('/')
        return
      }
    }
    if (isForgotPassword) {
      localStorage.setItem('forgotPassword', params.get('token'))
    } else {
      if (isPublicPage) {
        handleLogoutWithToast(pathname)
        return
      }
      if (pathCookie) {
        if (isPrivate && localStorage.getItem('username')) {
          navigate(pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : fullPath, {
            replace: pathname.endsWith('/')
          })
        } else {
          if (paymentCheck) {
            navigate(pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : fullPath, {
              replace: pathname.endsWith('/')
            })
          } else if (isCasinoGamesPage || isPrivate) {
            if (!isVipApproved) {
              navigate('/')
            } else navigate(`/${searchParams}`)
          } else {
            navigate(pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : fullPath, {
              replace: pathname.endsWith('/')
            })
          }
        }
        return
      }

      // No cookie present
      if (isCasinoGamesPage) {
        handleLogoutAndNavigate(
          pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : `/online-social-casino-games${searchParams}`
        )
      }
      // else {
      //   if (!isVipApproved) {
      //     navigate('/online-social-casino-games')
      //   } else handleLogoutAndNavigate(`/online-social-casino-games${searchParams}`)
      // }
    }

    // Memoize public routes check
    const publicRoutes = ['/games', '/blog', '/faq', '/about-us', '/contact-us']
    if (publicRoutes.some((route) => pathname.includes(route))) {
      handleLogoutAndNavigate(pathname)
      return
    }

    if (!pathname.includes('/game') && !pathname.includes('/cms')) {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [
    location.pathname,
    location.search,
    redirectHandled,
    navigate,
    isUserAuthenticate,
    pathChecks,
    userDetails,
    isVipApproved,
    handleLogoutWithToast,
    handleLogoutAndNavigate
  ])

  // Memoize logout handlers to prevent unnecessary re-renders
  const handleLogoutAndNavigate = useCallback((navigateTo = '/') => {
    closePortal()
    if (accessCookie) {
      deleteAccessTokenCookies('accessToken')
    }
    logout()
    navigate(navigateTo)
  }, [closePortal, accessCookie, logout, navigate])

  const handleLogoutWithToast = useCallback((navigateTo = '/') => {
    closePortal()
    if (accessCookie) {
      deleteAccessTokenCookies('accessToken')
    }
    logout()
    navigate(navigateTo)
  }, [closePortal, accessCookie, logout, navigate])

  return (
    <div>
      <Grid className='main-page'>
        {showHeader && (
          <LayoutWrapper>
            <Suspense fallback={<HeaderSkeleton />}>
              <Header />
            </Suspense>
          </LayoutWrapper>
        )}
        <Grid className={`${classes.lobbyWrap} ${socketRibbonData?.isRibbon ? 'msg-header' : ''}`}>
          {showSidebarAndFooter && (
            <LayoutWrapper>
              <Suspense fallback={<SidebarSkeleton />}>
                <SideBar />
              </Suspense>
            </LayoutWrapper>
          )}
          <Suspense fallback={<Loader />}>
            <Routes>{applicationRoutes}</Routes>
          </Suspense>
        </Grid>
        {showSidebarAndFooter && (
          <LayoutWrapper>
            <Suspense fallback={<FooterSkeleton />}>
              <Footer />
            </Suspense>
          </LayoutWrapper>
        )}
      </Grid>
    </div>
  )
}

const AppRouter = React.memo(() => {
  const isAuthenticate = useUserStore((state) => state.isAuthenticate)
  isUserAuthenticate = isAuthenticate
  return buildRouter(appRoutes)
})

export default AppRouter
