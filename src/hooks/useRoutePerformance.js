import { useEffect, useRef } from 'react'
import { useLocation } from 'react-router-dom'

/**
 * Simple performance monitoring hook for route changes
 * Tracks navigation timing and provides basic metrics
 */
export const useRoutePerformance = (enabled = process.env.NODE_ENV === 'development') => {
  const location = useLocation()
  const navigationStartRef = useRef(null)
  const metricsRef = useRef({})

  useEffect(() => {
    if (!enabled) return

    // Mark navigation start
    navigationStartRef.current = performance.now()
    
    // Clear previous metrics for this route
    const routeKey = location.pathname
    metricsRef.current[routeKey] = {
      navigationStart: navigationStartRef.current,
      pathname: location.pathname,
      search: location.search
    }

    // Measure when route is fully loaded
    const measureRouteLoad = () => {
      if (navigationStartRef.current) {
        const loadTime = performance.now() - navigationStartRef.current
        metricsRef.current[routeKey].loadTime = loadTime
        
        // Log performance metrics in development
        if (process.env.NODE_ENV === 'development') {
          console.group(`🚀 Route Performance: ${location.pathname}`)
          console.log(`Navigation Time: ${loadTime.toFixed(2)}ms`)
          console.log(`Full Path: ${location.pathname}${location.search}`)
          
          // Check for performance issues
          if (loadTime > 1000) {
            console.warn('⚠️ Slow route detected (>1s)')
          } else if (loadTime > 500) {
            console.warn('⚠️ Route could be optimized (>500ms)')
          } else {
            console.log('✅ Good performance')
          }
          console.groupEnd()
        }
      }
    }

    // Use requestIdleCallback if available, otherwise setTimeout
    if (window.requestIdleCallback) {
      window.requestIdleCallback(measureRouteLoad)
    } else {
      setTimeout(measureRouteLoad, 0)
    }

    return () => {
      navigationStartRef.current = null
    }
  }, [location.pathname, location.search, enabled])

  // Return metrics for external use
  return {
    getMetrics: () => metricsRef.current,
    getCurrentRouteMetrics: () => metricsRef.current[location.pathname],
    clearMetrics: () => {
      metricsRef.current = {}
    }
  }
}

/**
 * Hook to track component render performance
 */
export const useRenderPerformance = (componentName, enabled = process.env.NODE_ENV === 'development') => {
  const renderStartRef = useRef(null)
  const renderCountRef = useRef(0)

  useEffect(() => {
    if (!enabled) return

    renderCountRef.current += 1
    const renderTime = renderStartRef.current ? performance.now() - renderStartRef.current : 0

    if (renderTime > 0 && process.env.NODE_ENV === 'development') {
      console.log(`🔄 ${componentName} render #${renderCountRef.current}: ${renderTime.toFixed(2)}ms`)
      
      if (renderTime > 16) {
        console.warn(`⚠️ ${componentName} slow render (>16ms) - consider optimization`)
      }
    }
  })

  // Mark render start
  renderStartRef.current = performance.now()

  return {
    renderCount: renderCountRef.current,
    markRenderStart: () => {
      renderStartRef.current = performance.now()
    }
  }
}

export default useRoutePerformance
