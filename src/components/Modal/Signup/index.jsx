import * as React from 'react'
import { useEffect, useState , lazy, Suspense} from 'react'
import {
  Close as CloseIcon,
  Visibility,
  VisibilityOff,
  Check as CheckIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material'
import PropTypes from 'prop-types'
import {
  Grid,
  Box,
  InputAdornment,
  Link,
  CircularProgress,
  OutlinedInput,
  Button,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Accordion,
  AccordionDetails,
  AccordionSummary
} from '@mui/material'
import useStyles from './Signup.styles'
import Age from '../../ui-kit/icons/svg/age.svg'
import promocodeIcon from '../../ui-kit/icons/svg/promocode.svg'
import appleIcon from '../../ui-kit/icons/svg/apple.svg'
import facebookIcon from '../../ui-kit/icons/svg/facebook.svg'
import googleIcon from '../../ui-kit/icons/svg/google.svg'

import { useForm } from 'react-hook-form'
import { useUserStore } from '../../../store/useUserSlice'
import { usePortalStore } from '../../../store/userPortalSlice'
import { yupResolver } from '@hookform/resolvers/yup'
import { userSignUpSchema } from '../../../pages/Lobby/components/SignUpForm/schema'
import toast from 'react-hot-toast'
import { useLocation, useNavigate } from 'react-router-dom'
import { CasinoQuery, useGoogleLoginMutation, useSignUpMutation } from '../../../reactQuery'
import {
  useGetDailyBonusMutation,
  useGetFreeSpinMutation,
  useGetPromotionBonusMutation,
  useGetWelcomeBonusMutation
} from '../../../reactQuery/bonusQuery'
import Signin from '../Signin'
import DailyBonus from '../../DailyBonus'
import { useGoogleLogin } from '@react-oauth/google'
import {
  useAppleLoginMutation,
  useCheckPromocodeMutation,
  useFacebookLoginMutation
} from '../../../reactQuery/authQuery'
import { getBrowserType } from '../../../utils/helpers'
import AppleLogin from 'react-apple-login'
import PasswordErrorMsg from '../../ReusableComponents/PasswordErrorMsg'
import OtpVerificationUI from './OtpVerificationUI'
import WelcomeBonus from '../../WelcomeBonus'
import PromotionBonus from '../../DailyBonus/promotionBonus'
import { deleteCookie, deleteRefferalCookie, getCookie, setCookie } from '../../../utils/cookiesCollection'
import CmsModal from '../../CmsModal/CmsModal'
import Popup2Fa from '../../../pages/popup2Fa'
import GeocomplyPopup from '../../../Context/GeocomplyPopup'
import useAuthStore from '../../../store/useAuthStore'
import useSeon from '../../../utils/useSeon'
import TagManager from 'react-gtm-module'
import { useJackpotStore, useSubCategoryOnLoadStore } from '../../../store/store'
import ScratchCardComponent from '../../ScratchCard/ScratchCardComponent'
import { usePragmaticJackpotStore } from '../../../store/usePragmaticJackpot'
import ModalLoader from '../../ui-kit/ModalLoader'
const FreeSpinModal = lazy(() => import('../../FreeSpinModal/FreeSpinModal'));

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {/* <Typography>{children}</Typography> */}
          {children}
        </Box>
      )}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

const facebookAppId = import.meta.env.VITE_FB_APP_ID
const AppleClientId = import.meta.env.VITE_APPLE_CLIENT_ID
const RedirectUrl = import.meta.env.VITE_APPLE_REDIRECT_URL

export default function Signup({ email }) {
  const classes = useStyles()
  const pathCookie = getCookie('path')
  const navigate = useNavigate()
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const portalStore = usePortalStore((state) => state)
  const setIsAuthenticate = useUserStore((state) => state.setIsAuthenticate)
  const [appleState, setAppleState] = useState({})
  const [showSignUpUI, setShowSignUpUi] = useState(true)
  const [isFormSubmitting, setIsFormSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState({ password: false })
  const [userEmail, setUserEmail] = useState('')
  const [affiliateCodeCookieValue, setAffiliateCodeCookieValue] = useState('')
  const [affiliateIdCookieValue, setAffilitaeIdCookieValue] = useState('')
  const [affiliatePromocodeCookieValue, setAffilitaePromocodeCookieValue] = useState('')
  const { setAuthenticated, setPathCookieCheck } = useAuthStore()
  const state = useSubCategoryOnLoadStore((state) => state)
  const { setPragmaticJackpotGc, setPragmaticJackpotSc } = usePragmaticJackpotStore()
  const {
    register,
    formState: { errors, isValid },
    handleSubmit: handleSignUpSubmit,
    watch,
    setValue
  } = useForm({
    defaultValues: { email: '' || email, password: '', promocode: '' },
    resolver: yupResolver(userSignUpSchema),
    mode: 'onChange'
  })
  const isreferralcode = getCookie('referralcode')
  const params = new URLSearchParams(window.location.search)
  const referralcode = params.get('referralcode') || isreferralcode || ''
  const affiliateCode = params.get('btag') || ''
  const affiliateId = params.get('affid') || ''
  const affiliatePromocode = params.get('promocode') || ''
  const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toUTCString() // 15 days from now
  // const { isNotAvailable, fetchEncryptedLocation, isRegionAllowed } = useGeolocation()
  const sessionId = useSeon()
  const emailValue = watch('email')
  const password = watch('password')
  const promocode = watch('promocode')

  if (affiliateCode || affiliateId || affiliatePromocode) {
    document.cookie = `affiliateCode=${affiliateCode}; expires=${expires}`
    document.cookie = `affiliateId=${affiliateId}; expires=${expires}`
    document.cookie = `affiliatePromocode=${affiliatePromocode}; expires=${expires}`
  }

  useEffect(() => {
    handleReadAffiliateCodeCookie()
    handleReadAffiliateIdCookie()
    handleReadAffiliatePromocodeCookie()
  }, [])

  useEffect(() => {
    if (referralcode !== '') {
      toast.success('Please use this signup page to make sure your friend gets a referral bonus', {
        duration: 3000
      })
    }
  }, [referralcode])

  // Function to read a cookie
  const handleReadAffiliateCodeCookie = () => {
    const name = 'affiliateCode='
    const decodedCookie = decodeURIComponent(document.cookie)
    const cookieArray = decodedCookie.split('; ')
    for (let i = 0; i < cookieArray.length; i++) {
      let cookie = cookieArray[i]
      if (cookie.indexOf(name) === 0) {
        setAffiliateCodeCookieValue(cookie.substring(name.length, cookie.length))
        return
      }
    }
  }
  const handleReadAffiliateIdCookie = () => {
    const name = 'affiliateId='
    const decodedCookie = decodeURIComponent(document.cookie)
    const cookieArray = decodedCookie.split('; ')
    for (let i = 0; i < cookieArray.length; i++) {
      let cookie = cookieArray[i]
      if (cookie.indexOf(name) === 0) {
        setAffilitaeIdCookieValue(cookie.substring(name.length, cookie.length))
        return
      }
    }
  }
  const handleReadAffiliatePromocodeCookie = () => {
    const name = 'affiliatePromocode='
    const decodedCookie = decodeURIComponent(document.cookie)
    const cookieArray = decodedCookie.split('; ')
    for (let i = 0; i < cookieArray.length; i++) {
      let cookie = cookieArray[i]
      if (cookie.indexOf(name) === 0) {
        setValue('promocode', cookie.substring(name.length, cookie.length))
        setAffilitaePromocodeCookieValue(cookie.substring(name.length, cookie.length))
        return
      }
    }
  }

  const [promoCodeValid, setPromoCodeValid] = useState('')
  const location = useLocation()
  const handleReset = () => {
    setValue('email', '')
    setValue('password', '')
    setValue('promocode', '')
  }

  const { data: subCategories, isLoading, refetch } = CasinoQuery.getSubcategoryListQuery({ params: {} })
  useEffect(() => {
    setCookie('onloadGameApi', true)
    state.setSubCategories(subCategories?.data)
    setPragmaticJackpotGc(subCategories?.pragmaticActiveJackpotDetails?.gcType)
    setPragmaticJackpotSc(subCategories?.pragmaticActiveJackpotDetails?.scType)
    state.setIsLoading(isLoading)
    state.setRefetch(refetch)
  }, [subCategories, isLoading, refetch])

  const handleClose = (event, reason) => {
    if (reason && reason === 'backdropClick') return
    refetch()
    portalStore.closePortal()
    handleReset()
  }

  useEffect(() => {
    window.fbAsyncInit = () => {
      window.FB.init({
        appId: facebookAppId,
        autoLogAppEvents: true,
        xfbml: true,
        version: 'v11.0'
      })
    }
    ;(function (d, s, id) {
      var js,
        fjs = d.getElementsByTagName(s)[0]
      if (d.getElementById(id)) {
        return
      }
      js = d.createElement(s)
      js.id = id
      js.src = 'https://connect.facebook.net/en_US/sdk.js'
      fjs.parentNode.insertBefore(js, fjs)
    })(document, 'script', 'facebook-jssdk')
  }, [])

  const handleClickShowPassword = (field) => {
    setShowPassword((prevShowPassword) => ({
      ...prevShowPassword,
      [field]: !prevShowPassword[field]
    }))
  }

  const handleMouseDownPassword = (event) => {
    event.preventDefault()
  }

  const onSignUpSubmit = (data) => {
    setUserEmail(data.email)
    setIsFormSubmitting(true)
    signUpMutation.mutate({
      email: data.email,
      password: btoa(data.password),
      isTermsAccepted: true,
      browser: getBrowserType(),
      referralCode: referralcode,
      affiliateCode: affiliateCodeCookieValue,
      affiliateId: affiliateIdCookieValue,
      platform: '',
      promocode: data.promocode || affiliatePromocodeCookieValue,
      sessionKey: sessionId,
      rtyuioo: sessionId === ' ' ? true : false
    })
  }

  const openLogin = () => {
    portalStore.openPortal(() => <Signin />, 'loginModal')
  }

  const handlePromoCodeChange = (event) => {
    const value = event.target.value
    setValue('promocode', value)
  }
  const handleGeocomplyPopup = (data) => {
    portalStore.openPortal(() => <GeocomplyPopup open={true} errorData={data} />, 'tournamentEndPopup')
  }
  const signUpMutation = useSignUpMutation({
    onSuccess: (res) => {
      TagManager.dataLayer({
        dataLayer: {
          event: 'signup',
          email: res?.data?.user?.email
        }
      })
      localStorage.setItem('allowedUserAccess', true)
      setShowSignUpUi(false)
      setIsFormSubmitting(false)
      setCookie('path', '/home', 30)
      setAuthenticated(true)
      setPathCookieCheck(true)
      deleteRefferalCookie('referralcode')
      if (affiliateCodeCookieValue || affiliateIdCookieValue || affiliatePromocodeCookieValue) {
        deleteCookie('affiliateCode')
        deleteCookie('affiliateId')
        deleteCookie('affiliatePromocode')
      }
      toast.success('An OTP has been successfully sent to your email address', {
        duration: 3000
      })
    },
    onError: (err) => {
      //navigate('/home')
      setIsFormSubmitting(false)
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        errors.forEach(({ description, errorCode }) => {
          if (description) {
            TagManager.dataLayer({
              dataLayer: {
                event: 'signup_error',
                email: emailValue,
                error: description
              }
            })
            // if (errorCode === 3007) {
            // toast.error("This email is already registered. Please use a different email address for sign up", {
            //   duration: 3000
            // })
            // } else
            if (errorCode === 3047) {
              localStorage.setItem('allowedUserAccess', false)
              // toast.error(description)
              handleClose()
            }
            // else toast.error(description,  {
            //   duration: 3000
            // })
          }
        })
      } else if (
        err?.response?.data?.data?.state === 'DECLINE' ||
        err?.response?.data?.data?.ipDetails?.vpn === true ||
        err?.response?.data?.data?.ipDetails?.web_proxy === true
      ) {
        localStorage.setItem('allowedUserAccess', false)
        handleGeocomplyPopup(err?.response?.data?.data)
      }
    }
  })
  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (res?.data?.data) {
        let resetData = res?.data?.data?.remainingTime
        portalStore.openPortal(() => <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />, 'bonusStreak')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {}
  })

  const mutationGetWelcomeBonus = useGetWelcomeBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(() => <WelcomeBonus welcomeBonusData={res?.data?.data} />, 'bonusModal')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetPromotionBonus = useGetPromotionBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(() => <PromotionBonus promotionBonusData={res?.data?.data} />, 'bonusModal')
      } else {
        handleClose()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })
     const mutationGetFreeSpin = useGetFreeSpinMutation ({
      onSuccess: (res) => {   
        if(res?.data?.freeSpinBonus.length > 0){
 portalStore.openPortal(
      () => (
        <Suspense fallback={<ModalLoader/>}>
          <FreeSpinModal data={res?.data?.freeSpinBonus} />
        </Suspense>
      ),
      'freeSpinModal'
    );
        }
        
      },
      onError: (error) => {
        console.log(error)
      }
    })

  // JACKPOT - STORE
  const { setJackpotOn, setJackpotMultiplier } = useJackpotStore()
  const afterSignUp = (res) => {
    const userData = res?.data?.user
    setUserDetails(userData)
    localStorage.setItem('username', userData?.username)
    localStorage.setItem('phoneVerified', userData?.phoneVerified)
    setJackpotOn(userData?.isJackpotOptedIn)
    setJackpotMultiplier(userData?.jackpotMultiplier)
    setCookie('path', '/home', 30)
    setAuthenticated(true)
    setPathCookieCheck(true)
    deleteRefferalCookie('referralcode')
    setIsAuthenticate(true)
    if (affiliateCodeCookieValue || affiliateIdCookieValue || affiliatePromocodeCookieValue) {
      deleteCookie('affiliateCode')
      deleteCookie('affiliateId')
      deleteCookie('affiliatePromocode')
    }
    handleClose()
    window.location.reload()
    if (userData?.isPromotionBonusAllowed && !userData?.promotionBonusClaimedAt) {
      mutationGetPromotionBonus.mutate()
    } else if (!userData?.isWelcomeBonusClaimed && userData?.username) {
      mutationGetWelcomeBonus.mutate()
    } else if (!userData?.isDailyBonusClaimed && userData?.username) {
      mutationGetDailyBonus.mutate()
    } else if (!userData?.isScratchCardBonusClaimed && userData?.username) {
      portalStore.openPortal(
        () => (
          <ScratchCardComponent
            scratchCardBonus={userData?.scratchCardBonusData?.scratchCardBonus}
            userBonusId={userData?.scratchCardBonusData?.userBonusId}
            rewardType={userData?.scratchCardBonusData?.rewardType}
          />
        ),
        'bonusStreak'
      )
      
    }
     else if (userData?.isFreeSpinBonusApplicable && userData?.username ) {
            
                mutationGetFreeSpin.mutate()
              
            }
  }

  const mutationGoogle = useGoogleLoginMutation({
    onSuccess: (res) => {
      // console.log('GOOGLE SIGNUP :::', res)
      // if (import.meta.env.VITE_NODE_ENV === 'staging' || import.meta.env.VITE_NODE_ENV === 'production') {
      //   fetchEncryptedLocation(res?.data?.user?.userId, GEOCOMPLY_REASON_CODE.REGISTRATION)
      // }
      if (res?.data?.user?.authEnable) {
        portalStore.openPortal(() => <Popup2Fa res={res} afterLogin={afterSignUp} />, 'valutModal')
        return
      }
      localStorage.setItem('allowedUserAccess', true)
      afterSignUp(res)
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        errors.forEach(({ description, errorCode }) => {
          if (description) {
            // if (errorCode === 3007) {
            // toast.error('You have already registered. Please login.')
            // } else if (errorCode === 3185) {
            // toast.error(description, duration);
            // }
          }
        })
      } else if (
        err?.response?.data?.data?.state === 'DECLINE' ||
        err?.response?.data?.data?.ipDetails?.vpn === true ||
        err?.response?.data?.data?.ipDetails?.web_proxy === true
      ) {
        localStorage.setItem('allowedUserAccess', false)
        handleGeocomplyPopup(err?.response?.data?.data)
      }
    }
  })

  const appleResponse = (response) => {
    if (!response.error) {
      if (response) {
        const userData = {
          ...response,
          isSignup: true,
          isTermsAccepted: true,
          referralCode: referralcode,
          affiliateCode: affiliateCodeCookieValue,
          affiliateId: affiliateIdCookieValue,
          promocode: promocode || affiliatePromocodeCookieValue,
          sessionKey: sessionId,
          rtyuioo: sessionId === ' ' ? true : false
        }
        mutationApple.mutate(userData)
      }
      setAppleState({ authResponse: response })
    }
  }

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      if (tokenResponse) {
        handleClose()
        const userData = {
          credential: tokenResponse.access_token,
          isSignup: true,
          isTermsAccepted: true,
          referralCode: referralcode,
          affiliateCode: affiliateCodeCookieValue,
          affiliateId: affiliateIdCookieValue,
          promocode: promocode || affiliatePromocodeCookieValue,
          sessionKey: sessionId
        }
        mutationGoogle.mutate(userData)
      }
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        errors.forEach(({ description }) => {
          if (description) {
            // toast.error(description, duration);
          }
        })
      }
    }
  })

  const mutationApple = useAppleLoginMutation({
    onSuccess: (res) => {
      const userData = res?.data?.user
      // if (import.meta.env.VITE_NODE_ENV === 'staging' || import.meta.env.VITE_NODE_ENV === 'production') {
      //   fetchEncryptedLocation(userData?.userId, GEOCOMPLY_REASON_CODE.REGISTRATION)
      // }
      localStorage.setItem('allowedUserAccess', true)
      setUserDetails(userData)
      localStorage.setItem('username', userData?.username)
      localStorage.setItem('phoneVerified', userData?.phoneVerified)
      setCookie('path', '/home', 30)
      setAuthenticated(true)
      setPathCookieCheck(true)
      deleteRefferalCookie('referralcode')
      setIsAuthenticate(true)
      if (affiliateCodeCookieValue || affiliateIdCookieValue || affiliatePromocodeCookieValue) {
        deleteCookie('affiliateCode')
        deleteCookie('affiliateId')
        deleteCookie('affiliatePromocode')
      }

      handleClose()
      window.location.reload()
      if (userData?.isPromotionBonusAllowed && !userData?.promotionBonusClaimedAt) {
        mutationGetPromotionBonus.mutate()
      } else if (!userData?.isWelcomeBonusClaimed && userData?.username) {
        mutationGetWelcomeBonus.mutate()
      } else if (!userData?.isDailyBonusClaimed && userData?.username) {
        mutationGetDailyBonus.mutate()
      } else if (!userData?.isScratchCardBonusClaimed && userData?.username) {
        portalStore.openPortal(
          () => (
            <ScratchCardComponent
              scratchCardBonus={userData?.scratchCardBonusData?.scratchCardBonus}
              userBonusId={userData?.scratchCardBonusData?.userBonusId}
              rewardType={userData?.scratchCardBonusData?.rewardType}
            />
          ),
          'bonusStreak'
        )
      }
       else if (userData?.isFreeSpinBonusApplicable && userData?.username ) {
            
                mutationGetFreeSpin.mutate()
              
            }
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        errors.forEach(({ description }) => {
          if (description) {
            // toast.error(description, duration);
          }
        })
      } else if (
        err?.response?.data?.data?.state === 'DECLINE' ||
        err?.response?.data?.data?.ipDetails?.vpn === true ||
        err?.response?.data?.data?.ipDetails?.web_proxy === true
      ) {
        localStorage.setItem('allowedUserAccess', false)
        handleGeocomplyPopup(err?.response?.data?.data)
      }
    }
  })

  const mutationFacebook = useFacebookLoginMutation({
    onSuccess: (res) => {
      const userData = res?.data?.user
      // if (import.meta.env.VITE_NODE_ENV === 'staging' || import.meta.env.VITE_NODE_ENV === 'production') {
      //   fetchEncryptedLocation(userData?.userId, GEOCOMPLY_REASON_CODE.REGISTRATION)
      // }
      localStorage.setItem('allowedUserAccess', true)
      setUserDetails(userData)
      localStorage.setItem('username', userData?.username)
      localStorage.setItem('phoneVerified', userData?.phoneVerified)
      setCookie('path', '/home', 30)
      setAuthenticated(true)
      setPathCookieCheck(true)

      deleteRefferalCookie('referralcode')
      setIsAuthenticate(true)
      if (affiliateCodeCookieValue || affiliateIdCookieValue || affiliatePromocodeCookieValue) {
        deleteCookie('affiliateCode')
        deleteCookie('affiliateId')
        deleteCookie('affiliatePromocode')
      }
      handleClose()
      window.location.reload()
      if (userData?.isPromotionBonusAllowed && !userData?.promotionBonusClaimedAt) {
        mutationGetPromotionBonus.mutate()
      } else if (!userData?.isWelcomeBonusClaimed && userData?.username) {
        mutationGetWelcomeBonus.mutate()
      } else if (!userData?.isDailyBonusClaimed && userData?.username) {
        mutationGetDailyBonus.mutate()
      } else if (!userData?.isScratchCardBonusClaimed && userData?.username) {
        portalStore.openPortal(
          () => (
            <ScratchCardComponent
              scratchCardBonus={userData?.scratchCardBonusData?.scratchCardBonus}
              userBonusId={userData?.scratchCardBonusData?.userBonusId}
              rewardType={userData?.scratchCardBonusData?.rewardType}
            />
          ),
          'bonusStreak'
        )
      }
       else if (userData?.isFreeSpinBonusApplicable && userData?.username ) {
            
                mutationGetFreeSpin.mutate()
              
            }
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        errors.forEach(({ description, errorCode }) => {
          if (description) {
            // if (errorCode === 3007) {
            // toast.error('You have already registered. Please login.')
            // }
          }
        })
      } else if (
        err?.response?.data?.data?.state === 'DECLINE' ||
        err?.response?.data?.data?.ipDetails?.vpn === true ||
        err?.response?.data?.data?.ipDetails?.web_proxy === true
      ) {
        localStorage.setItem('allowedUserAccess', false)
        handleGeocomplyPopup(err?.response?.data?.data)
      }
    }
  })

  const responseFacebook = (response) => {
    if (response) {
      const userData = {
        firstName: response.first_name,
        lastName: response.last_name,
        userId: response.id,
        email: response.email,
        isSignup: true,
        isTermsAccepted: true,
        referralCode: referralcode,
        affiliateCode: affiliateCodeCookieValue,
        affiliateId: affiliateIdCookieValue,
        promocode: promocode || affiliatePromocodeCookieValue,
        sessionKey: sessionId,
        rtyuioo: sessionId === ' ' ? true : false
      }
      mutationFacebook.mutate(userData)
    }
  }

  const handleFacebookLoginCredential = () => {
    window.FB.login(function (response) {
      if (response && response.authResponse && response.authResponse.userID) {
        window.FB.api(
          `/${response.authResponse.userID}`,
          { fields: ['first_name', 'last_name', 'email'] },
          function (_response) {
            responseFacebook(_response)
          }
        )
      }
    })
  }

  const handleFacebookLogin = () => {
    FB.getLoginStatus(function (response) {
      handleFacebookLoginCredential()
    })
  }
  const mutationCheckPromocode = useCheckPromocodeMutation({
    onSuccess: (res) => {
      setPromoCodeValid(res?.data?.isPromocodeValid)
      TagManager.dataLayer({
        dataLayer: {
          event: 'promocode',
          email: emailValue,
          promocode: promocode
        }
      })
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        errors.forEach(({ description }) => {
          if (description) {
            TagManager.dataLayer({
              dataLayer: {
                event: 'promocode_failed',
                email: emailValue,
                promocode: promocode,
                error: description
              }
            })
            // toast.error(description, duration)
          }
        })
      }
    }
  })
  const handleCheckPromoCode = () => {
    const data = { promocode: promocode }
    mutationCheckPromocode.mutate(data)
  }
  const handleTNC = () => {
    portalStore.closePortal()
    if (pathCookie === '/home') {
      navigate('/cms/about-terms')
    } else {
      portalStore.openPortal(() => <CmsModal path={'/cms/about-terms'} fromLanding={true} />, 'cmsModal')
    }
  }

  return (
    <Grid>
      <Grid>
        <DialogContent sx={{ padding: '0' }}>
          <Grid className='modal-section'>
            <Grid sx={{ width: '100%' }}>
              <DialogTitle sx={{ m: 0, p: 2 }} id='customized-dialog-title'>
                {showSignUpUI ? 'Sign Up' : 'You are almost there!'}
              </DialogTitle>
              <IconButton aria-label='close' onClick={handleClose} className='modal-close-btn'>
                <CloseIcon />
              </IconButton>

              {showSignUpUI ? (
                <form onSubmit={handleSignUpSubmit(onSignUpSubmit)} name='SignUp'>
                  <Box sx={{ width: '100%' }} style={{ padding: '0' }} className={classes.modalWrapper}>
                    <CustomTabPanel value={0} index={0} style={{ padding: '0' }}>
                      <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                        <OutlinedInput
                          variant='outlined'
                          id='Email_Address'
                          className={classes.passCheckIcon}
                          {...register('email')}
                          placeholder='Email Address'
                          inputProps={{ 'data-tracking': 'Signup.EnterEmail.Fld' }}
                          endAdornment={
                            <InputAdornment position='end'>
                              <IconButton style={{ color: '#fff' }} aria-label='email visibility' edge='end'>
                                {errors?.email?.message && emailValue?.length > 0 ? (
                                  <CloseIcon style={{ color: 'red' }} />
                                ) : (
                                  ''
                                )}
                                {!errors?.email?.message && emailValue?.length > 0 ? (
                                  <CheckIcon style={{ color: 'green' }} />
                                ) : (
                                  ''
                                )}
                              </IconButton>
                            </InputAdornment>
                          }
                          autoComplete='email'
                        />
                        <Typography className={classes.errorLabel}>{errors?.email?.message}</Typography>
                      </Grid>
                      <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                        <OutlinedInput
                          variant='outlined'
                          id='password'
                          className={classes.passEyeIcon}
                          {...register('password')}
                          placeholder='Password'
                          onKeyDown={(evt) => evt.keyCode === 32 && evt.preventDefault()}
                          // onPaste={handlePasswordPaste}
                          type={showPassword.password ? 'text' : 'password'}
                          inputProps={{ 'data-tracking': 'Signup.EnterPassword.Fld' }}
                          endAdornment={
                            <InputAdornment position='end'>
                              <IconButton
                                style={{ color: '#fff', opacity: !password ? 0.5 : 1 }}
                                aria-label='toggle password visibility'
                                onClick={() => handleClickShowPassword('password')}
                                onMouseDown={handleMouseDownPassword}
                                edge='end'
                                disabled={!password}
                              >
                                {showPassword.password ? <Visibility /> : <VisibilityOff />}
                              </IconButton>
                            </InputAdornment>
                          }
                          autoComplete='new-password'
                        />
                        {password?.length === 0 ? (
                          <Typography className={classes.errorLabel}>{errors?.password?.message}</Typography>
                        ) : (
                          <PasswordErrorMsg password={password} errors={errors} />
                        )}
                      </Grid>

                      <Grid className={classes.termCondition} style={{ marginBottom: '15px' }}>
                        <span>
                          By clicking Sign up, Continue with Facebook, Continue with Google or Continue with Apple, you
                          agree to our{' '}
                          <span style={{ fontWeight: '700' }}>
                            <Link onClick={handleTNC} className='conditionText'>
                              T & C
                            </Link>
                          </span>
                          .
                        </span>
                      </Grid>
                      {!referralcode && (
                        <Grid className={classes.promoCodeParent}>
                          <Accordion className='accordionSummary'>
                            <AccordionSummary
                              expandIcon={<ExpandMoreIcon />}
                              aria-controls='panel1-content'
                              id='panel1-header'
                              className='promoCode'
                              data-tracking='Signup.IHaveCode.Spoiler'
                            >
                              <img style={{ padding: '5px' }} src={promocodeIcon} />I have a promo code
                            </AccordionSummary>
                            <AccordionDetails className='accordionDetails' style={{ color: 'white' }}>
                              <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                                <OutlinedInput
                                  variant='outlined'
                                  id='PromoCode'
                                  className={classes.passCheckIcon}
                                  {...register('promocode')}
                                  placeholder='Enter Promocode'
                                  onChange={handlePromoCodeChange}
                                  inputProps={{ 'data-tracking': 'Signup.Promocode.Fld' }}
                                  endAdornment={
                                    <InputAdornment position='end'>
                                      <IconButton
                                        style={{ color: '#fff' }}
                                        aria-label='promocode visibility'
                                        edge='end'
                                      >
                                        {promoCodeValid === false ? (
                                          <CloseIcon style={{ color: 'red' }} />
                                        ) : promoCodeValid === true ? (
                                          <CheckIcon style={{ color: 'green' }} />
                                        ) : (
                                          ' '
                                        )}
                                      </IconButton>
                                    </InputAdornment>
                                  }
                                  autoComplete='off'
                                />
                                <Grid className={classes.promoVerifyBtn}>
                                  <Button
                                    variant='contained'
                                    onClick={() => handleCheckPromoCode()}
                                    data-tracking='Signup.VerifyPromocode.Btn'
                                  >
                                    Verify Promocode
                                  </Button>
                                </Grid>
                                <Typography className={classes.errorLabel}>{errors?.promocode?.message}</Typography>
                              </Grid>
                            </AccordionDetails>
                          </Accordion>
                        </Grid>
                      )}
                    </CustomTabPanel>
                  </Box>

                  <Box className={classes.bottomSection}>
                    <Grid className={classes.submitBtn}>
                      <Button
                        variant='contained'
                        type='submit'
                        disabled={isFormSubmitting || !isValid}
                        style={{ opacity: isFormSubmitting || !isValid ? 0.5 : 1 }}
                        data-tracking='Signup.ClickToSignUp.Btn'
                      >
                        <span>Sign up</span>
                        {isFormSubmitting && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
                      </Button>
                    </Grid>

                    <Grid display='flex' gap={1} justifyContent={'center'} className={classes.buttonGrp}>
                      <Button
                        variant='contained'
                        onClick={() => handleGoogleLogin()}
                        data-tracking='Signup.GoogleLogin.Btn'
                      >
                        <img src={googleIcon} />
                      </Button>
                      {import.meta.env.VITE_NODE_ENV === 'production' ? (
                        <></>
                      ) : (
                        <Button
                          variant='contained'
                          onClick={() => handleFacebookLogin()}
                          data-tracking='Signup.FacebookLogin.Btn'
                        >
                          <img src={facebookIcon} />
                        </Button>
                      )}
                      <AppleLogin
                        clientId={AppleClientId}
                        redirectURI={RedirectUrl}
                        usePopup={true}
                        callback={appleResponse}
                        scope='email name'
                        responseMode='query'
                        render={(renderProps) => (
                          <Button
                            variant='contained'
                            onClick={renderProps.onClick}
                            data-tracking='Signup.AppleLogin.Btn'
                          >
                            <img src={appleIcon} />
                          </Button>
                        )}
                      />
                    </Grid>
                    <Grid className={classes.dontAccount}>
                      <Typography>
                        Already Have an Account?{' '}
                        <Link onClick={openLogin} data-tracking='Signup.LoginHere.Link'>
                          {' '}
                          Log In{' '}
                        </Link>{' '}
                        here!
                      </Typography>
                    </Grid>
                    <Grid
                      style={{ display: 'flex', justifyContent: 'center', alignItemsL: 'center', marginTop: '20px' }}
                    >
                      <img src={Age} alt='adult' />
                    </Grid>
                  </Box>
                </form>
              ) : (
                <OtpVerificationUI userEmail={userEmail} handleClose={handleClose} />
              )}
            </Grid>
          </Grid>
          :
        </DialogContent>
      </Grid>
    </Grid>
  )
}
