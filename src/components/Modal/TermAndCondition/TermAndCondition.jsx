import React, { useState, lazy, Suspense } from 'react'
import {
  <PERSON>ton,
  <PERSON>rid,
  <PERSON>po<PERSON>,
  DialogContent,
  Checkbox,
  FormControlLabel,
  Box,
  FormLabel,
  List,
  ListItem,
  Link,
  Tooltip
} from '@mui/material'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { termAndConditionCheckSchema } from '../Signup/SignUpSchema'
import { usePortalStore } from '../../../store/userPortalSlice'
import { FooterQuerys, useGetProfileMutation } from '../../../reactQuery'
import useStyles from './TermAndCondition.styles'
import {
  useCmsMutation,
  useGetDailyBonusMutation,
  useGetFreeSpinMutation,
  useGetPromotionBonusMutation,
  useGetWelcomeBonusMutation
} from '../../../reactQuery/bonusQuery'
import { useUserStore } from '../../../store/useUserSlice'
import { toast } from 'react-hot-toast'
import TermsAndConditionImage from '../../ui-kit/icons/utils/TermsAndConditionImage.webp'
import {
  KeyboardArrowRight as KeyboardArrowRightIcon,
  KeyboardArrowLeft as KeyboardArrowLeftIcon
} from '@mui/icons-material'
import parse from 'html-react-parser'
import PaymentModal from '../../../pages/Store/PaymentModal'
import WelcomeBonus from '../../WelcomeBonus'
import DailyBonus from '../../DailyBonus'
import PromotionBonus from '../../DailyBonus/promotionBonus'
import ScratchCardComponent from '../../ScratchCard/ScratchCardComponent'
import ModalLoader from '../../ui-kit/ModalLoader'
const FreeSpinModal = lazy(() => import('../../FreeSpinModal/FreeSpinModal'))

/* eslint-disable multiline-ternary */

const CmsUpdateModal = ({ fromCoinBundles, packageDetails }) => {
  const classes = useStyles()
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const [termsModel, setTermsModel] = useState(false)
  const [content, setContent] = useState('')
  const [slug, setSlug] = useState('')
  const portalStore = usePortalStore((state) => state)

  const { data: cmsLinks } = FooterQuerys.getCmsQuery()
  const cmsData = cmsLinks?.length > 0 ? cmsLinks?.filter((info) => !info?.isHidden && info.slug === 'about-terms') : []
  const { data: cmsContent } = FooterQuerys.getCmsContentQuery({ pageSlug: 'about-terms' })
  const {
    register,
    formState: { errors },
    handleSubmit,
    watch
  } = useForm({ resolver: yupResolver(termAndConditionCheckSchema), mode: 'onChange' })

  const checkboxChecked = watch('checkbox', false)

  const handleClose = () => {
    portalStore.closePortal()
  }
  const htmlRenderer = (htmlContent) => {
    return parse(htmlContent)
  }
  const mutationGetWelcomeBonus = useGetWelcomeBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(() => <WelcomeBonus welcomeBonusData={res?.data?.data} />, 'bonusModal')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (res?.data?.data) {
        let resetData = res?.data?.data?.remainingTime
        portalStore.openPortal(() => <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />, 'bonusStreak')
      } else {
        portalStore.closePortal()
      }
    },
    onError: () => {}
  })
  const mutationGetPromotionBonus = useGetPromotionBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(() => <PromotionBonus promotionBonusData={res?.data?.data} />, 'bonusModal')
      } else {
        handleClose()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })
  const mutationGetFreeSpin = useGetFreeSpinMutation({
    onSuccess: (res) => {
      if(res?.data?.freeSpinBonus.length > 0){

        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader/>}>
              <FreeSpinModal data={res?.data?.freeSpinBonus} />
            </Suspense>
          ),
          'freeSpinModal'
        )
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const handlefutherModal = (res) => {
    const userData = res?.data?.data
    if (fromCoinBundles) {
      ;(function () {
        window._conv_q = window._conv_q || []
        _conv_q.push(['pushRevenue', 'credit', packageDetails, '100466670'])
      })()
      portalStore.openPortal(() => <PaymentModal packageDetails={packageDetails} />, 'innerModal')
    } else if (userData?.isPromotionBonusAllowed && !userData?.promotionBonusClaimedAt) {
      mutationGetPromotionBonus.mutate()
    } else if (userData?.isWelcomeBonusAllowed && !userData?.isWelcomeBonusClaimed) {
      mutationGetWelcomeBonus.mutate()
    } else if (userData?.isDailyBonusAllowed && !userData?.isDailyBonusClaimed) {
      mutationGetDailyBonus.mutate()
    } else if (!userData?.isScratchCardBonusClaimed) {
      portalStore.openPortal(
        () => (
          <ScratchCardComponent
            scratchCardBonus={userData?.scratchCardBonusData?.scratchCardBonus}
            userBonusId={userData?.scratchCardBonusData?.userBonusId}
            rewardType={userData?.scratchCardBonusData?.rewardType}
          />
        ),
        'bonusStreak'
      )
    } else if (userData?.isFreeSpinBonusApplicable && userData?.username) {
      mutationGetFreeSpin.mutate()
    }
  }
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      portalStore.closePortal()
      handlefutherModal(res)
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })
  const mutationCmsData = useCmsMutation({
    onSuccess: (res) => {
      toast.success(res?.data?.message)
      getProfileMutation.mutate()
    },
    onError: () => {
      portalStore.closePortal()
    }
  })

  const acceptTermNCondition = (data) => {
    mutationCmsData.mutate(data)
  }

  const handleFormSubmit = (data) => {
    const updatedData = { ...data, isTermsAccepted: true }
    acceptTermNCondition(updatedData)
  }
  const handleCMSData = (data) => {
    setSlug(cmsData?.find((x) => x?.slug === data).title?.EN)
    setContent(cmsContent?.content?.EN)
    setTermsModel(true)
  }
  return (
    <DialogContent className={classes.redeemModal}>
      <Grid container className={classes.termsAndConditionMainContainer}>
        <Grid
          item
          xs={12}
          sm={5}
          md={5}
          display='flex'
          alignItems='end'
          justifyContent='center'
          className='terms-image'
        >
          <Box
            style={{
              height: '440px',
              width: 'auto',
              objectFit: 'contain'
            }}
          >
            <img
              src={TermsAndConditionImage}
              alt=''
              style={{
                height: '100%',
                width: '100%',
                objectFit: 'contain'
              }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={12} md={7}>
          {!termsModel ? (
            <Box className={classes.redeemModalContainer}>
              <Grid display='flex' alignItems='center' justifyContent='space-between'>
                <Typography variant='heading'>Important Update </Typography>
                {/* <Grid className='modal-close'>
                  <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
                    <CloseIcon className='Arrow-right-icon' />
                  </IconButton>
                </Grid> */}
              </Grid>
              <form onSubmit={handleSubmit(handleFormSubmit)}>
                <Grid display='flex' justifyContent='space-between'>
                  <Grid className='leftSection'>
                    <Grid className='inputWrapContainer'>
                      <Grid className='inputWrap'>
                        <FormLabel>
                          There have been changes to our Terms of Service. Please review and accept these changes to
                          continue.
                        </FormLabel>
                      </Grid>

                      <Grid
                        className='footer-cms-wrap'
                        style={{
                          height: '45px'
                        }}
                      >
                        <List className={classes.contentSectionList}>
                          <ListItem disablePadding>
                            <Link
                              className='linkListItems'
                              data-tracking='AfterLogin.ImportantUpdate.TermsAndConditions'
                              onClick={() => handleCMSData(cmsData[0]?.slug)}
                            >
                              {cmsData[0]?.title?.EN}
                              <KeyboardArrowRightIcon className='Arrow-right-icon' />
                            </Link>
                          </ListItem>
                        </List>
                      </Grid>
                    </Grid>
                    <Grid className='themeCheckBoxWrap'>
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...register('checkbox')}
                            checked={checkboxChecked}
                            inputProps={{
                              'data-tracking': 'AfterLogin.ImportantUpdate.Confirm.Checkbox'
                            }}
                          />
                        }
                        label='I confirm that I have read, accept and agree to be bound by the updated Terms of Service.'
                        sx={{ color: '#fff', fontWeight: 700 }}
                      />
                      {errors?.checkbox && (
                        <p style={{ paddingTop: '3px' }} className={classes.inputError}>
                          {errors?.checkbox?.message}
                        </p>
                      )}
                    </Grid>

                    <Grid className='btn-wrap' style={{ display: 'flex', justifyContent: 'center' }}>
                      <Button
                        variant='contained'
                        className='btn-primary'
                        style={{ width: '100%', color: checkboxChecked ? '' : 'rgba(0, 0, 0, 0.26)' }}
                        disabled={!checkboxChecked}
                        onClick={() => handleFormSubmit()}
                        data-tracking='AfterLogin.ImportantUpdate.Submit.Btn'
                      >
                        <span>Submit</span>
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </form>
            </Box>
          ) : (
            <Box>
              <Box className={classes.redeemModalContainer2}>
                <Grid display='flex' alignItems='center'>
                  <Tooltip title='Back to Important Update'>
                    <KeyboardArrowLeftIcon className='Arrow-right-icon' onClick={() => setTermsModel(false)} />
                  </Tooltip>
                  <Typography variant='heading'> {slug} </Typography>
                </Grid>
                <Grid className='inputWrapContainer'>
                  <Grid
                    className='inputWrap'
                    style={{
                      maxHeight: '360px',
                      overflowY: 'scroll'
                    }}
                  >
                    <FormLabel>{htmlRenderer(content)}</FormLabel>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          )}
        </Grid>
      </Grid>
    </DialogContent>
  )
}

export default CmsUpdateModal
