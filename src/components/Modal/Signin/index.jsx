import React, { useEffect, useState, lazy, Suspense } from 'react'
import PropTypes from 'prop-types'
import { Link, useNavigate } from 'react-router-dom'

// MUI Components
import {
  Box,
  Button,
  CircularProgress,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  InputAdornment,
  OutlinedInput,
  Typography
} from '@mui/material'
import { Close as CloseIcon, Visibility, VisibilityOff, Check as CheckIcon } from '@mui/icons-material'

// Third-party
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useGoogleLogin } from '@react-oauth/google'
import toast from 'react-hot-toast'
import AppleLogin from 'react-apple-login'
import TagManager from 'react-gtm-module'

// Styles & Hooks
import useStyles from './Signin.styles'
import useAuthStore from '../../../store/useAuthStore'
import useSeon from '../../../utils/useSeon'

// Stores
import { usePortalStore } from '../../../store/userPortalSlice'
import { useCoinStore, useJackpotStore, useSubCategoryOnLoadStore, useUserStore } from '../../../store/store'

import Age from '../../ui-kit/icons/svg/age.svg'
import googleIcon from '../../ui-kit/icons/svg/google.svg'
import facebookIcon from '../../ui-kit/icons/svg/facebook.svg'
import appleIcon from '../../ui-kit/icons/svg/apple.svg'

import { userLogInSchema, userLogInMobileSchema } from '../../../pages/Lobby/components/LoginForm/schema'

// Queries & Mutations
// import {
//   useGetDailyBonusMutation,
//   useGetPromotionBonusMutation,
//   useGetWelcomeBonusMutation,
//   useGoogleLoginMutation,
//   useLoginMutation,
//   CasinoQuery
// } from '../../../reactQuery'

import {
  useGetDailyBonusMutation,
  useGetFreeSpinMutation,
  useGetPromotionBonusMutation,
  useGetWelcomeBonusMutation
} from '../../../reactQuery/bonusQuery'
import { CasinoQuery, useDynamoKey, useGoogleLoginMutation, useLoginMutation } from '../../../reactQuery'
import { useAppleLoginMutation, useFacebookLoginMutation } from '../../../reactQuery/authQuery'

// Utilities
import { setWithExpiry } from '../../../utils/helpers'
import { setItem } from '../../../utils/storageUtils'
import { deleteCookie, getCookie, getVipRoute, setCookie } from '../../../utils/cookiesCollection'
import Loader from '../../Loader'
import casinoQuery from '../../../reactQuery/casinoQuery'
import TimeBreakPopup from '../../../pages/Lobby/TimeBreakPopup'
import ScratchCardComponent from '../../ScratchCard/ScratchCardComponent'
import { usePragmaticJackpotStore } from '../../../store/usePragmaticJackpot'
import ModalLoader from '../../ui-kit/ModalLoader'
// import FreeSpinModal from '../../FreeSpinModal/FreeSpinModal'
const FreeSpinModal = lazy(() => import('../../FreeSpinModal/FreeSpinModal'));

// Components
const Signup = lazy(() => import('../Signup'))
const DailyBonus = lazy(() => import('../../DailyBonus'))
const WelcomeBonus = lazy(() => import('../../WelcomeBonus'))
const PromotionBonus = lazy(() => import('../../DailyBonus/promotionBonus'))
const OtpVerificationUI = lazy(() => import('../Signup/OtpVerificationUI'))
const TermAndCondition = lazy(() => import('../TermAndCondition/TermAndCondition'))
const SpecialPurchaseModal = lazy(() => import('../../SpecialPurchaseModal'))
const ForgotPasswordForm = lazy(() => import('../../../pages/ForgotPassword'))
const PaymentModal = lazy(() => import('../../../pages/Store/PaymentModal'))
const Popup2Fa = lazy(() => import('../../../pages/popup2Fa'))
const GeocomplyPopup = lazy(() => import('../../../Context/GeocomplyPopup'))

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {/* <Typography>{children}</Typography> */}
          {children}
        </Box>
      )}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

const facebookAppId = import.meta.env.VITE_FB_APP_ID
const AppleClientId = import.meta.env.VITE_APPLE_CLIENT_ID
const RedirectUrl = import.meta.env.VITE_APPLE_REDIRECT_URL

export default function Signin({ fromCoinBundles, selectedPackages }) {
  const classes = useStyles()
  const navigate = useNavigate()
  const subCategoryState = useSubCategoryOnLoadStore((state) => state)
  const portalStore = usePortalStore((state) => state)
  const [value, setaValue] = useState(0)
  const [appleState, setAppleState] = useState({})
  const [loggedInFrom, setLoggedInFrom] = useState('')
  const user = useUserStore((state) => state)
  const setCoin = useCoinStore((state) => {
    return state.setCoinType
  })
  const sessionId = useSeon()
  const [showLoginUI, setShowLoginUI] = useState(true)
  const [userEmail, setUserEmail] = useState(true)
  const duration = { duration: 3000 }
  const { setAuthenticated, setPathCookieCheck } = useAuthStore()
  const vipRoute = getVipRoute('vipRoute')
  let vipEmail = ''
  if (vipRoute) {
    const urlObj = new URL(vipRoute, window.location.origin)
    vipEmail = urlObj.searchParams.get('email') || ''
  }
  const { setPragmaticJackpotGc, setPragmaticJackpotSc } = usePragmaticJackpotStore()
  const {
    handleSubmit,
    register,
    formState: { errors, isValid },
    setValue,
    watch
  } = useForm({
    defaultValues: { email: vipEmail || '', password: '' },
    resolver: yupResolver(value === 0 ? userLogInSchema : userLogInMobileSchema),
    mode: 'onChange'
  })
  const password = watch('password')
  const emailValue = watch('email')

  const appleResponse = (response) => {
    if (!response.error) {
      responseApple(response)
      setAppleState({ authResponse: response })
    }
  }

  const handleReset = () => {
    setValue('email', '')
    setValue('password', '')
  }
  const [showPassword, setShowPassword] = useState({
    password: false
  })
  const handleClickShowPassword = (field) => {
    setShowPassword((prevShowPassword) => ({
      ...prevShowPassword,
      [field]: !prevShowPassword[field]
    }))
  }
  const handleMouseDownPassword = (event) => {
    event.preventDefault()
  }

  const handleForgotPasswordOpen = () => {
    portalStore.openPortal(
      () => (
        <Suspense fallback={<Loader />}>
          <ForgotPasswordForm />
        </Suspense>
      ),
      'loginModal'
    )
  }

  const handleClose = (event, reason) => {
    if (reason && reason === 'backdropClick') return
    handleReset()
    portalStore.closePortal()
  }
  const mutationGetWelcomeBonus = useGetWelcomeBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<Loader />}>
              <WelcomeBonus welcomeBonusData={res?.data?.data} />{' '}
            </Suspense>
          ),
          'bonusModal'
        )
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (res?.data?.data) {
        let resetData = res?.data?.data?.remainingTime
        portalStore.openPortal(
          () => (
            <Suspense fallback={<Loader />}>
              <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />
            </Suspense>
          ),
          'bonusStreak'
        )
      } else {
        portalStore.closePortal()
      }
    },
    onError: () => {}
  })
  const mutationGetPromotionBonus = useGetPromotionBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<Loader />}>
              <PromotionBonus promotionBonusData={res?.data?.data} />
            </Suspense>
          ),
          'bonusModal'
        )
      } else {
        handleClose()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })
   const mutationGetFreeSpin = useGetFreeSpinMutation ({
    onSuccess: (res) => {   
      if(res?.data?.freeSpinBonus.length > 0){

        portalStore.openPortal(
      () => (
        <Suspense fallback={<ModalLoader/>}>
          <FreeSpinModal data={res?.data?.freeSpinBonus} />
        </Suspense>
      ),
      'freeSpinModal'
    ); 

      }
    },
    onError: (error) => {
      console.log(error)
    }
  })
  const { data: subCategories, isLoading, refetch } = CasinoQuery.getSubcategoryListQuery({ params: {} })
  useEffect(() => {
    setCookie('onloadGameApi', true)
    subCategoryState.setSubCategories(subCategories?.data)
    setPragmaticJackpotGc(subCategories?.pragmaticActiveJackpotDetails?.gcType)
    setPragmaticJackpotSc(subCategories?.pragmaticActiveJackpotDetails?.scType)
    subCategoryState.setIsLoading(isLoading)
    subCategoryState.setRefetch(refetch)
  }, [subCategories, isLoading, refetch])

  const dynamoKeySend = useDynamoKey({
    onSuccess: (res) => {
      if (res.data.success) {
        toast.success('Message Sent')
        deleteCookie('dynamoKey')
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  // JACKPOT - STORE
  const { setJackpotOn, setJackpotMultiplier } = useJackpotStore()

  const afterLogin = (res) => {
    const userData = res?.data?.user

    let dynamoKey = getCookie('dynamoKey')
    if (res?.data?.user?.timeBreak) {
      portalStore.openPortal(() => <TimeBreakPopup timeBreakData={res?.data?.message} />, 'dynamoBonusModal')
    }
    if (!userData?.isEmailVerified) {
      setShowLoginUI(false)
      return
    }

    // Show success toast and set localStorage
    toast.success('You have successfully logged in', duration)
    localStorage.setItem('username', userData?.username)
    localStorage.setItem('phoneVerified', userData?.phoneVerified)

    // Update user store
    user.setUserDetails(userData)
    user.setIsAuthenticate(true)

    setJackpotOn(userData?.isJackpotOptedIn)
    setJackpotMultiplier(userData?.jackpotMultiplier)
    // Navigate and close login portal
    if (vipRoute) {
      navigate(vipRoute)
    } else navigate('/')
    portalStore.closePortal()
    if (dynamoKey !== '') {
      dynamoKeySend.mutate({ d10x_link_id: dynamoKey })
    }
    // Handle terms & conditions
    if (!userData?.isTermsAccepted) {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<Loader />}>
            <TermAndCondition fromCoinBundles={fromCoinBundles} packageDetails={selectedPackages} />
          </Suspense>
        ),

        'termsNConditionModal'
      )
      return
    }

    // Handle coin bundle purchase
    if (fromCoinBundles) {
      window._conv_q = window._conv_q || []
      window._conv_q.push(['pushRevenue', 'credit', selectedPackages, '100466670'])

      portalStore.openPortal(
        () => (
          <Suspense fallback={<Loader />}>
            {' '}
            <PaymentModal packageDetails={selectedPackages} />
          </Suspense>
        ),
        'innerModal'
      )
      return
    }

    // Handle bonuses
    const { username } = userData

    if (userData?.isPromotionBonusAllowed && !userData?.promotionBonusClaimedAt) {
      mutationGetPromotionBonus.mutate()
      return
    }

    if (!userData?.isWelcomeBonusClaimed && userData?.isWelcomeBonusAllowed && username) {
      mutationGetWelcomeBonus.mutate()
      return
    }

    if (!userData?.isDailyBonusClaimed && userData?.isDailyBonusAllowed && username) {
      mutationGetDailyBonus.mutate()
      return
    }
    if (!userData?.isScratchCardBonusClaimed && username) {
      portalStore.openPortal(
        () => (
          <ScratchCardComponent
            scratchCardBonus={userData?.scratchCardBonusData?.scratchCardBonus}
            userBonusId={userData?.scratchCardBonusData?.userBonusId}
            rewardType={userData?.scratchCardBonusData?.rewardType}
          />
        ),
        'bonusStreak'
      )
      return
    }
   if (userData?.isFreeSpinBonusApplicable && username) {
             
                mutationGetFreeSpin.mutate()
               return
            }
          
    if (userData?.welcomePurchaseBonusApplicable && username) {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<Loader />}>
            <SpecialPurchaseModal />
          </Suspense>
        ),
        'termsNConditionModal'
      )
    }

    // // Show success toast and set localStorage
    // toast.success('You have successfully logged in', duration)
    // localStorage.setItem('username', userData?.username)
    // localStorage.setItem('phoneVerified', userData?.phoneVerified)

    // // Update user store
    // user.setUserDetails(userData)
    // user.setIsAuthenticate(true)

    // // Navigate and close login portal
    // navigate('/')
    // portalStore.closePortal()

    // // Handle terms & conditions
    // if (!userData?.isTermsAccepted) {
    //   portalStore.openPortal(
    //     () => (
    //       <Suspense fallback={<Loader />}>
    //         <TermAndCondition fromCoinBundles={fromCoinBundles} packageDetails={selectedPackages} />
    //       </Suspense>
    //     ),

    //     'termsNConditionModal'
    //   )
    //   return
    // }

    // Handle coin bundle purchase
    if (fromCoinBundles) {
      window._conv_q = window._conv_q || []
      window._conv_q.push(['pushRevenue', 'credit', selectedPackages, '100466670'])

      portalStore.openPortal(
        () => (
          <Suspense fallback={<Loader />}>
            {' '}
            <PaymentModal packageDetails={selectedPackages} />
          </Suspense>
        ),
        'innerModal'
      )
      return
    }

    if (userData?.isPromotionBonusAllowed && !userData?.promotionBonusClaimedAt) {
      mutationGetPromotionBonus.mutate()
      return
    }

    if (!userData?.isWelcomeBonusClaimed && userData?.isWelcomeBonusAllowed && username) {
      mutationGetWelcomeBonus.mutate()
      return
    }

    if (!userData?.isDailyBonusClaimed && userData?.isDailyBonusAllowed && username) {
      mutationGetDailyBonus.mutate()
      return
    }
    if (!userData?.isScratchCardBonusClaimed && username) {
      portalStore.openPortal(
        () => (
          <ScratchCardComponent
            scratchCardBonus={userData?.scratchCardBonusData?.scratchCardBonus}
            userBonusId={userData?.scratchCardBonusData?.userBonusId}
            rewardType={userData?.scratchCardBonusData?.rewardType}
          />
        ),
        'bonusStreak'
      )
      return
    }
     if (userData?.isFreeSpinBonusApplicable && username) {
             
                mutationGetFreeSpin.mutate()
               return
            }
          
    if (userData?.welcomePurchaseBonusApplicable && username) {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<Loader />}>
            <SpecialPurchaseModal />
          </Suspense>
        ),
        'termsNConditionModal'
      )
    }

    // Final fetch
    subcategoryListMutation.mutate()
  }

  /* success response of subcategoryListMutation */
  const successToggler = (data) => {
    subCategoryState.setSubCategories(data?.data?.data)
  }
  /* error response of subcategoryListMutation */
  const errorToggler = (error) => {
    // setGameDataLoading(false)
    console.log(error)
    // toast.error(error?.response?.data?.errors?.[0]?.description)
  }
  /* subcategoryListMutation api hit  */
  const subcategoryListMutation = casinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })

  const onLoginSuccess = (res) => {
    const user = res?.data?.user
    const userIdStr = user?.userId?.toString()
    const email = user?.email

    const userLoginData = res?.data?.user
    TagManager.dataLayer({
      dataLayer: {
        event: 'login',
        loginMethod: loggedInFrom,
        email,
        user_id: user?.userId
      }
    })

    // Optimove SDK event reporting
    try {
      if (typeof optimoveSDK !== 'undefined' && optimoveSDK.API) {
        optimoveSDK.API.reportEvent('login', { email }, null, userIdStr)
      } else {
        console.warn('OPTIMOVE ERROR: SDK or API is not defined for customEvent')
      }
    } catch (error) {
      console.error('OPTIMOVE ERROR in customEvent:', error)
    }

    // Set session-related data
    setCoin('SC')
    setWithExpiry('loginTime')
    setItem('special_package', true)
    localStorage.setItem('allowedUserAccess', true)
    setCookie('path', '/home', 30)
    setPathCookieCheck(true)
    refetch()
    // 2FA check
    if (user?.authEnable) {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<Loader />}>
            <Popup2Fa res={res} afterLogin={afterLogin} />
          </Suspense>
        ),
        'valutModal'
      )
      return
    }
    setAuthenticated(true)
    afterLogin(res)
  }

  const handleGeocomplyPopup = (data) => {
    portalStore.openPortal(
      () => (
        <Suspense fallback={<Loader />}>
          {' '}
          <GeocomplyPopup open={true} errorData={data} />
        </Suspense>
      ),
      'tournamentEndPopup'
    )
  }

  const onLoginError = (error) => {
    const errors = error?.response?.data?.errors ?? []

    if (errors.length > 0) {
      errors.forEach(({ description }) => {
        TagManager.dataLayer({
          dataLayer: {
            event: 'login_error',
            email: emailValue,
            error: description
          }
        })
      })
      return
    }

    const data = error?.response?.data?.data
    if (data?.state === 'DECLINE' || data?.ipDetails?.vpn || data?.ipDetails?.web_proxy) {
      localStorage.setItem('allowedUserAccess', false)
      handleGeocomplyPopup(data)
    }
  }

  const handleOnSubmit = (data) => {
    setUserEmail(data.email)
    setLoggedInFrom('email')
    mutation.mutate({
      email: data.email,
      password: btoa(data.password),
      sessionKey: sessionId,
      rtyuioo: sessionId === ' ' ? true : false
    })
  }

  const mutation = useLoginMutation({
    onSuccess: onLoginSuccess,
    onError: onLoginError
  })
  const openSignUp = () => {
    portalStore.openPortal(
      () => (
        <Suspense fallback={<Loader />}>
          <Signup />
        </Suspense>
      ),
      'loginModal'
    )
  }

  const mutationGoogle = useGoogleLoginMutation({
    onSuccess: onLoginSuccess,
    onError: onLoginError
  })

  const mutationFacebook = useFacebookLoginMutation({
    onSuccess: onLoginSuccess,
    onError: (errorResponse) => {
      onLoginError(errorResponse)
    }
  })

  const mutationApple = useAppleLoginMutation({
    onSuccess: onLoginSuccess,
    onError: (errorResponse) => {
      onLoginError(errorResponse)
    }
  })

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      setLoggedInFrom('google')
      mutationGoogle.mutate({
        credential: tokenResponse.access_token,
        isSignup: false,
        sessionKey: sessionId,
        rtyuioo: sessionId === ' ' ? true : false
      })
    },
    onError: (errorResponse) => {
      onLoginError(errorResponse)
    }
  })

  const responseFacebook = (response) => {
    setLoggedInFrom('facebook')
    mutationFacebook.mutate({
      firstName: response.first_name,
      lastName: response.last_name,
      userId: response.id,
      email: response.email,
      isSignup: false,
      sessionKey: sessionId,
      rtyuioo: sessionId === ' ' ? true : false
    })
  }

  const responseApple = (response) => {
    setLoggedInFrom('apple')
    mutationApple.mutate({
      ...response,
      isSignup: false,
      sessionKey: sessionId,
      rtyuioo: sessionId === ' ' ? true : false
    })
  }

  const handleFacebookLoginCredential = () => {
    window.FB.login(function (response) {
      if (response && response.authResponse && response.authResponse.userID) {
        window.FB.api(
          `/${response.authResponse.userID}`,
          { fields: ['first_name', 'last_name', 'email'] },
          function (_response) {
            responseFacebook(_response)
          }
        )
      }
    })
  }

  const handleFacebookLogin = () => {
    FB.getLoginStatus(function (response) {
      handleFacebookLoginCredential()
    })
  }

  //Initiating the Facebook sdk
  useEffect(() => {
    window.fbAsyncInit = () => {
      window.FB.init({
        appId: facebookAppId,
        autoLogAppEvents: true,
        xfbml: true,
        version: 'v11.0'
      })
    }
    ;(function (d, s, id) {
      var js,
        fjs = d.getElementsByTagName(s)[0]
      if (d.getElementById(id)) {
        return
      }
      js = d.createElement(s)
      js.id = id
      js.src = 'https://connect.facebook.net/en_US/sdk.js'
      fjs.parentNode.insertBefore(js, fjs)
    })(document, 'script', 'facebook-jssdk')
  }, [])

  return (
    <Grid>
      <Grid>
        <DialogContent sx={{ padding: '0' }}>
          <Grid className='modal-section'>
            <Grid sx={{ width: '100%' }}>
              <DialogTitle sx={{ m: 0, p: 2 }} id='customized-dialog-title' style={{ marginBottom: '1.25rem' }}>
                {showLoginUI ? 'Login' : 'You are almost there!'}
              </DialogTitle>
              <IconButton aria-label='close' onClick={handleClose} className='modal-close-btn'>
                <CloseIcon />
              </IconButton>
              {showLoginUI && (
                <form onSubmit={handleSubmit(handleOnSubmit)} name='SignIn'>
                  <Box sx={{ width: '100%' }} style={{ padding: '0' }} className={classes.modalWrapper}>
                    <CustomTabPanel value={0} index={0} style={{ padding: '0' }}>
                      <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                        <OutlinedInput
                          variant='outlined'
                          id='Email Address'
                          className={classes.passCheckIcon}
                          {...register('email')}
                          placeholder='Email Address'
                          autoComplete='email'
                          inputProps={{ 'data-tracking': 'Login.EnterEmail.Fld' }}
                          endAdornment={
                            <InputAdornment position='end'>
                              <IconButton style={{ marginRight: '.1875rem' }} aria-label='email visibility' edge='end'>
                                {errors?.email?.message && emailValue?.length > 0 ? (
                                  <CloseIcon style={{ color: 'red' }} />
                                ) : (
                                  ''
                                )}
                                {!errors?.email?.message && emailValue?.length > 0 ? (
                                  <CheckIcon style={{ color: 'green' }} />
                                ) : (
                                  ''
                                )}
                              </IconButton>
                            </InputAdornment>
                          }
                        />
                        <Typography className={classes.errorLabel}>{errors?.email && errors?.email.message}</Typography>
                      </Grid>
                      <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                        <OutlinedInput
                          variant='outlined'
                          id='password'
                          className={classes.passEyeIcon}
                          {...register('password')}
                          placeholder='Password'
                          autoComplete='new-password'
                          onKeyDown={(evt) => evt.keyCode === 32 && evt.preventDefault()}
                          // onPaste={handlePasswordPaste}
                          type={showPassword.password ? 'text' : 'password'}
                          inputProps={{ 'data-tracking': 'Login.EnterPassword.Fld' }}
                          endAdornment={
                            <InputAdornment position='end'>
                              <IconButton
                                style={{ color: '#fff', opacity: !password ? 0.5 : 1 }}
                                aria-label='toggle password visibility'
                                onClick={() => handleClickShowPassword('password')}
                                onMouseDown={handleMouseDownPassword}
                                edge='start'
                                disabled={!password}
                              >
                                {showPassword.password ? <Visibility /> : <VisibilityOff />}
                              </IconButton>
                            </InputAdornment>
                          }
                        />

                        {/* <Typography className={classes.errorLabel}>
                              {
                                password?.length === 0 ? <Typography className={classes.errorLabel}>{errors?.password && errors?.password?.message}</Typography> : <PasswordErrorMsg password={password} errors={errors} />
                              }
                            </Typography> */}

                        <Grid className={classes.forgetLink}>
                          <Link onClick={handleForgotPasswordOpen} data-tracking='Login.ForgotPassword.Link'>
                            Forgot Password ?
                          </Link>
                        </Grid>
                      </Grid>
                    </CustomTabPanel>
                  </Box>

                  <Box className={classes.bottomSection}>
                    <Grid className={classes.submitBtn}>
                      <Button
                        variant='contained'
                        type='submit'
                        disabled={
                          mutation.isLoading ||
                          mutationGoogle.isLoading ||
                          mutationApple.isLoading ||
                          mutationFacebook.isLoading ||
                          !isValid
                        }
                        style={{ opacity: !isValid ? 0.5 : 1 }}
                        data-tracking='Login.ClickToLogin.Btn'
                      >
                        <span>Login</span>
                        {(mutation.isLoading ||
                          mutation.isLoading ||
                          mutationGoogle.isLoading ||
                          mutationApple.isLoading ||
                          mutationFacebook.isLoading) && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
                      </Button>
                    </Grid>

                    <Grid className={classes.loginWith}>
                      <Typography>Or Log In With</Typography>
                    </Grid>

                    <Grid display='flex' gap={1} justifyContent={'center'} className={classes.buttonGrp}>
                      <Button
                        variant='contained'
                        onClick={() => handleGoogleLogin()}
                        disabled={
                          mutation.isLoading ||
                          mutationGoogle.isLoading ||
                          mutationApple.isLoading ||
                          mutationFacebook.isLoading
                        }
                        data-tracking='Login.google-login.Btn'
                      >
                        <img src={googleIcon} />
                      </Button>
                      {import.meta.env.VITE_NODE_ENV === 'production' ? (
                        <></>
                      ) : (
                        <Button
                          variant='contained'
                          onClick={() => handleFacebookLogin()}
                          disabled={
                            mutation.isLoading ||
                            mutationGoogle.isLoading ||
                            mutationApple.isLoading ||
                            mutationFacebook.isLoading
                          }
                          data-tracking='Login.facebook-login.Btn'
                        >
                          <img src={facebookIcon} />
                        </Button>
                      )}
                      <AppleLogin
                        clientId={AppleClientId}
                        redirectURI={RedirectUrl}
                        usePopup={true}
                        callback={appleResponse}
                        scope='email name'
                        responseMode='query'
                        render={(renderProps) => (
                          <Button
                            variant='contained'
                            onClick={renderProps.onClick}
                            disabled={
                              mutation.isLoading ||
                              mutationGoogle.isLoading ||
                              mutationApple.isLoading ||
                              mutationFacebook.isLoading
                            }
                            data-tracking='Login.apple-login.Btn'
                          >
                            <img src={appleIcon} />
                          </Button>
                        )}
                      />
                    </Grid>
                    <Grid className={classes.dontAccount}>
                      <Typography>
                        Don't Have an Account?{' '}
                        <Link onClick={openSignUp} data-tracking='Login.Signup.link'>
                          {' '}
                          Sign up{' '}
                        </Link>
                      </Typography>
                      <Grid
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItemsL: 'center',
                          marginTop: '1.25rem'
                        }}
                      >
                        <img src={Age} alt='adult' />
                      </Grid>
                    </Grid>
                  </Box>
                </form>
              )}
              {!showLoginUI && (
                <Suspense fallback={<Loader />}>
                  <OtpVerificationUI userEmail={userEmail} handleClose={handleClose} />
                </Suspense>
              )}
            </Grid>
          </Grid>
        </DialogContent>
      </Grid>
    </Grid>
  )
}
