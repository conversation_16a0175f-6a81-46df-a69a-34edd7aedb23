import React from 'react'

/**
 * Lightweight skeleton loader components for better performance
 * Optimized for minimal bundle size and fast rendering
 */

// Header skeleton - matches approximate header height
export const HeaderSkeleton = () => (
  <div 
    style={{ 
      height: '60px', 
      backgroundColor: '#1a1a1a',
      borderBottom: '1px solid #333',
      display: 'flex',
      alignItems: 'center',
      padding: '0 20px'
    }}
    role="status"
    aria-label="Loading header"
  >
    <div 
      style={{
        width: '120px',
        height: '30px',
        backgroundColor: '#333',
        borderRadius: '4px'
      }}
    />
  </div>
)

// Sidebar skeleton - matches approximate sidebar dimensions
export const SidebarSkeleton = () => (
  <div 
    style={{ 
      width: '250px', 
      height: '100vh',
      backgroundColor: '#1a1a1a',
      borderRight: '1px solid #333',
      padding: '20px'
    }}
    role="status"
    aria-label="Loading sidebar"
  >
    {/* User profile skeleton */}
    <div style={{ marginBottom: '20px' }}>
      <div 
        style={{
          width: '60px',
          height: '60px',
          backgroundColor: '#333',
          borderRadius: '50%',
          marginBottom: '10px'
        }}
      />
      <div 
        style={{
          width: '120px',
          height: '16px',
          backgroundColor: '#333',
          borderRadius: '4px',
          marginBottom: '8px'
        }}
      />
      <div 
        style={{
          width: '80px',
          height: '14px',
          backgroundColor: '#333',
          borderRadius: '4px'
        }}
      />
    </div>
    
    {/* Navigation items skeleton */}
    {[...Array(6)].map((_, i) => (
      <div 
        key={i}
        style={{
          width: '100%',
          height: '40px',
          backgroundColor: '#333',
          borderRadius: '4px',
          marginBottom: '8px'
        }}
      />
    ))}
  </div>
)

// Footer skeleton - matches approximate footer height
export const FooterSkeleton = () => (
  <div 
    style={{ 
      height: '200px',
      backgroundColor: '#1a1a1a',
      borderTop: '1px solid #333',
      padding: '20px'
    }}
    role="status"
    aria-label="Loading footer"
  >
    <div style={{ display: 'flex', gap: '40px' }}>
      {[...Array(4)].map((_, i) => (
        <div key={i} style={{ flex: 1 }}>
          <div 
            style={{
              width: '80px',
              height: '18px',
              backgroundColor: '#333',
              borderRadius: '4px',
              marginBottom: '15px'
            }}
          />
          {[...Array(4)].map((_, j) => (
            <div 
              key={j}
              style={{
                width: '60px',
                height: '14px',
                backgroundColor: '#333',
                borderRadius: '4px',
                marginBottom: '8px'
              }}
            />
          ))}
        </div>
      ))}
    </div>
  </div>
)

// Generic content skeleton
export const ContentSkeleton = ({ height = '400px' }) => (
  <div 
    style={{ 
      height,
      backgroundColor: '#1a1a1a',
      padding: '20px'
    }}
    role="status"
    aria-label="Loading content"
  >
    <div 
      style={{
        width: '200px',
        height: '24px',
        backgroundColor: '#333',
        borderRadius: '4px',
        marginBottom: '20px'
      }}
    />
    {[...Array(3)].map((_, i) => (
      <div 
        key={i}
        style={{
          width: '100%',
          height: '16px',
          backgroundColor: '#333',
          borderRadius: '4px',
          marginBottom: '12px'
        }}
      />
    ))}
  </div>
)

export default {
  HeaderSkeleton,
  SidebarSkeleton,
  FooterSkeleton,
  ContentSkeleton
}
