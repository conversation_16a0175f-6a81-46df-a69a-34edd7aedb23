import React from 'react'
import Card<PERSON>ectionWrapper from './cardSection.styles'
import { Box, Grid, Typography } from '@mui/material'
import useDynamicGamepageStore from '../../../store/useDynamicGamepageStore'
import LazyImage from '../../../utils/lazyImage'

const CardSection = () => {
  const dynamicGamePageData = useDynamicGamepageStore((state) => state.dynamicGamePageData)

  const gameCardData = dynamicGamePageData?.dynamicGameDetails?.GamePageCards?.[0] || {}

  return (
    <section>
      <CardSectionWrapper>
        <Box className='fullWidthContainer card-wrap'>
          <Box className='maxWidthContainer'>
            <Typography variant='h2'>{gameCardData?.title}</Typography>
            <Typography variant='body1'>{gameCardData?.description}</Typography>

            <Box className='top-games-section'>
              <Grid container spacing={2}>
                {gameCardData?.image?.map((card, index) => (
                  <Grid item xs={12} md={4} key={index}>
                    <figure className='game-img'>
                      <LazyImage
                        src={card?.imageUrl}
                        alt='top-games'
                        onError={(e) => {
                          e.target.onerror = null
                          e.target.src = '../src/components/ui-kit/icons/webp/slot-machine-reels.webp'
                        }}
                      />
                    </figure>

                    <Typography variant='h3'>{card?.caption}</Typography>
                    <Typography variant='body1'>{card?.altTag}</Typography>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Box>
        </Box>
      </CardSectionWrapper>
    </section>
  )
}

export default CardSection
