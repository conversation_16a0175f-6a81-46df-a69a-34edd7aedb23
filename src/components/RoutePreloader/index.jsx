import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

/**
 * RoutePreloader - Intelligently preloads likely next routes
 * Based on user navigation patterns and route relationships
 */
const RoutePreloader = () => {
  const location = useLocation()

  useEffect(() => {
    const preloadRoutes = () => {
      const currentPath = location.pathname

      // Define route relationships for intelligent preloading
      const routePreloadMap = {
        '/': [
          () => import('../../pages/Landing'),
          () => import('../../pages/Lobby/Lobby')
        ],
        '/online-social-casino-games': [
          () => import('../../pages/Lobby/Lobby'),
          () => import('../../pages/GamePlay/GamePlay')
        ],
        '/lobby': [
          () => import('../../pages/GamePlay/GamePlay'),
          () => import('../../pages/Accounts')
        ],
        '/user/account-details': [
          () => import('../../pages/Accounts/components/ProfileSection'),
          () => import('../../pages/Accounts/components/BetHistorySection')
        ],
        '/games': [
          () => import('../../pages/GamePlay/GamePlay'),
          () => import('../../components/SeoLandingPages/GamesPage')
        ]
      }

      // Get preload functions for current route
      const preloadFunctions = routePreloadMap[currentPath] || []

      // Preload with low priority and delay to avoid blocking main thread
      preloadFunctions.forEach((preloadFn, index) => {
        setTimeout(() => {
          if (window.requestIdleCallback) {
            window.requestIdleCallback(() => {
              preloadFn().catch(() => {
                // Silently handle preload failures
              })
            })
          } else {
            // Fallback for browsers without requestIdleCallback
            setTimeout(() => {
              preloadFn().catch(() => {
                // Silently handle preload failures
              })
            }, 100 * (index + 1))
          }
        }, 1000 + (index * 500)) // Stagger preloads
      })
    }

    // Only preload after initial route is loaded
    const timer = setTimeout(preloadRoutes, 2000)

    return () => clearTimeout(timer)
  }, [location.pathname])

  return null
}

/**
 * Critical resource preloader for immediate needs
 */
export const CriticalRoutePreloader = () => {
  useEffect(() => {
    // Preload most critical routes immediately
    const criticalRoutes = [
      () => import('../../pages/Landing'),
      () => import('../../pages/Lobby/Lobby')
    ]

    criticalRoutes.forEach((preloadFn, index) => {
      setTimeout(() => {
        preloadFn().catch(() => {
          // Silently handle failures
        })
      }, index * 100)
    })
  }, [])

  return null
}

export default RoutePreloader
