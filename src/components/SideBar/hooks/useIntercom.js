import { useEffect, useCallback, useRef } from "react";

const useIntercom = (hideDefaultLauncher = true, lazyLoad = true) => {
  const intercomLoadedRef = useRef(false);
  const loadTimeoutRef = useRef(null);

  const loadIntercomScript = useCallback(() => {
    if (intercomLoadedRef.current || window.Intercom) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      // Set up Intercom stub function
      window.Intercom = function () {
        window.Intercom.q.push(arguments);
      };
      window.Intercom.q = [];

      // Configure Intercom settings
      window.intercomSettings = {
        app_id: 'truve12n',
        alignment: 'left',
        horizontal_padding: '20',
        vertical_padding: '50',
        hide_default_launcher: hideDefaultLauncher,
      };

      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = 'https://widget.intercom.io/widget/truve12n';
      script.async = true;
      script.defer = true;

      script.onload = () => {
        intercomLoadedRef.current = true;
        window.Intercom('boot', window.intercomSettings);
        resolve();
      };

      script.onerror = () => {
        console.warn('Failed to load Intercom widget');
        reject(new Error('Intercom script failed to load'));
      };

      // Use requestIdleCallback for better performance
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          document.head.appendChild(script);
        });
      } else {
        // Fallback for browsers without requestIdleCallback
        setTimeout(() => {
          document.head.appendChild(script);
        }, 100);
      }
    });
  }, [hideDefaultLauncher]);

  const initializeIntercom = useCallback(() => {
    if (lazyLoad) {
      // Lazy load after user interaction or page load
      const loadAfterInteraction = () => {
        loadIntercomScript().catch(console.warn);
        // Remove event listeners after first interaction
        document.removeEventListener('mousedown', loadAfterInteraction);
        document.removeEventListener('touchstart', loadAfterInteraction);
        document.removeEventListener('keydown', loadAfterInteraction);
      };

      // Load on user interaction
      document.addEventListener('mousedown', loadAfterInteraction, { passive: true });
      document.addEventListener('touchstart', loadAfterInteraction, { passive: true });
      document.addEventListener('keydown', loadAfterInteraction, { passive: true });

      // Fallback: Load after 3 seconds if no interaction
      loadTimeoutRef.current = setTimeout(() => {
        loadAfterInteraction();
      }, 3000);
    } else {
      // Load immediately if not lazy loading
      loadIntercomScript().catch(console.warn);
    }
  }, [lazyLoad, loadIntercomScript]);

  useEffect(() => {
    initializeIntercom();

    return () => {
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
      }

      // Clean up event listeners
      const loadAfterInteraction = () => {};
      document.removeEventListener('mousedown', loadAfterInteraction);
      document.removeEventListener('touchstart', loadAfterInteraction);
      document.removeEventListener('keydown', loadAfterInteraction);

      // Shutdown Intercom on unmount
      if (window.Intercom && intercomLoadedRef.current) {
        window.Intercom('shutdown');
      }
    };
  }, [initializeIntercom]);

  // Return utility functions
  return {
    showIntercom: useCallback(() => {
      if (window.Intercom) {
        window.Intercom('show');
      } else {
        // Load Intercom if not loaded and then show
        loadIntercomScript().then(() => {
          window.Intercom('show');
        }).catch(console.warn);
      }
    }, [loadIntercomScript]),

    hideIntercom: useCallback(() => {
      if (window.Intercom) {
        window.Intercom('hide');
      }
    }, []),

    updateIntercom: useCallback((data) => {
      if (window.Intercom) {
        window.Intercom('update', data);
      }
    }, [])
  };
};

export default useIntercom;
