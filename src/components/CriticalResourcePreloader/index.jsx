import { useEffect } from 'react';
import landingBannerImg from '../../components/ui-kit/icons/webp/landing-banner.webp';
import Brand<PERSON>ogo from '../../components/ui-kit/icons/brand/brand-logo.webp';

/**
 * CriticalResourcePreloader - Simple preloader for critical LCP images
 * Focused on essential preloading without complex logic for maximum performance
 */
const CriticalResourcePreloader = () => {
  useEffect(() => {
    // Simple preload for banner image (LCP element)
    const preloadBanner = () => {
      // Check if preload already exists
      const existingPreload = document.querySelector(`link[href="${landingBannerImg}"]`);
      if (existingPreload) return;

      // Create preload link with highest priority
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = landingBannerImg;
      link.fetchPriority = 'high';

      // Insert at the very beginning of head for maximum priority
      document.head.insertBefore(link, document.head.firstChild);
    };

    // Simple preload for brand logo
    const preloadBrandLogo = () => {
      const existingPreload = document.querySelector(`link[href="${BrandLogo}"]`);
      if (existingPreload) return;

      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = BrandLogo;
      link.fetchPriority = 'high';
      document.head.appendChild(link);
    };

    // Execute preloading immediately
    preloadBanner();
    preloadBrandLogo();
  }, []);

  return null;
};

export default CriticalResourcePreloader;
