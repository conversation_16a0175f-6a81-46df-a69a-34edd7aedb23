import React, { useEffect, useMemo, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
// import blogArticles from '../../../utils/blogArticles.json'
import {
  Box,
  Grid,
  Link,
  Typography,
  Accordion,
  AccordionDetails,
  AccordionSummary,
  CircularProgress
} from '@mui/material'
import { Add, Remove } from '@mui/icons-material'
import useStyles from '../styles'
import bloguser from '../../ui-kit/blogAssets/blog-avatar-male.webp'
import fbIcon from '../../../components/ui-kit/icons/svg/facebook-icon.svg'
import igIcon from '../../../components/ui-kit/icons/svg/instagram-icon.svg'
import xIcon from '../../../components/ui-kit/icons/svg/x-icon.svg'
import { welcomeImg } from '../../../../src/components/ui-kit/icons/opImages'
import SeoLandingFooter from '../SeoLandingFooter'
import parse from 'html-react-parser'
import toast from 'react-hot-toast'
// import LandingHeader from '../LandingHeader'
import { usePortalStore } from '../../../store/userPortalSlice'
import Signup from '../../Modal/Signup'
import telegramBlog from '../../../components/ui-kit/icons/svg/telegramBlog.svg'
import useBlogSection from './hook/useBlogSection'
import NotFoundPage from '../../../pages/NotFoundPage'
import { GeneralQuery } from '../../../reactQuery'
import { DefaultBlog } from '../../ui-kit/icons/opImages'
import useBlogStore from '../../../store/useBlogSection'
import LandingHeader from '../../../pages/Landing/LandingHeader'
import SeoHead from '../../../utils/seoHead'
import ArticleSchema from '../SchemaComponents/ArticleSchema'
import BreadcrumbSchema from '../SchemaComponents/BreadCrumbSchema'

const DynamicBlog = () => {
  const location = useLocation()
  const classes = useStyles()
  const [articleContent, setArticleContent] = useState(null)
  const pathName = location.pathname.split('/')[2]
  const currentPageURL = window.location.href
  const navigate = useNavigate()
  const portalStore = usePortalStore()
  const [notFound, setNotFound] = useState(false)
  const [expanded, setExpanded] = useState(1)

  const { getRelatedArticles, popularArticles, popularBlogs, formatDateToWords, setPopularBlogs } = useBlogSection()

  const handleWelcome = () => {
    portalStore.openPortal(() => <Signup />, 'loginModal')
  }

  const { blogs } = useBlogStore((state) => state)
  const [loading, setLoading] = useState(true)

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth' // Optional: Adds smooth scrolling animation
    })
  }

  const { data: dynamicData } = GeneralQuery.getBlogsQuery({
    params: { slug: pathName }
  })

  const handleChange = (panel) => {
    setExpanded(expanded === panel ? null : panel)
  }

  useEffect(() => {
    if (blogs) {
      const filteredPopularBlogs = blogs?.filter((blog) => blog?.isPopularBlog === true)
      setPopularBlogs((prev) => [...prev, ...filteredPopularBlogs])
    }
  }, [blogs])

  useEffect(() => {
    if (dynamicData) {
      setArticleContent(dynamicData?.data?.dynamicBlogDetails)
      setLoading(false)
    }
  }, [dynamicData, pathName])

  const htmlRenderer = (htmlContent) => {
    if (htmlContent) {
      return parse(htmlContent)
    }
  }
  const handleClick = (articleName, isDynamic) => {
    navigate(`/blog/${articleName}`)
    scrollToTop()
  }

  const shareOnInstagram = () => {
    navigator.clipboard.writeText(currentPageURL).then(() => {
      toast.success('Link copied to clipboard! Now you can paste it on Instagram.')
    })
  }

  const relatedArticles = useMemo(() => {
    return getRelatedArticles(articleContent?.metaTitle, blogs)
  }, [articleContent?.metaTitle, blogs])

  if (notFound) {
    return <NotFoundPage />
  }

  return (
    <>
      {/* <Helmet>
        <title>{articleContent?.metaTitle}</title>
        <meta name='description' content={articleContent?.metaDescription} />
        <meta name='robots' content='index, follow' />
      </Helmet> */} 
      <SeoHead
        title={
          articleContent?.metaTitle
            ? `${articleContent?.metaTitle}`
            : 'The Money Factory Blog | Insights on Social Casino Games & Free Play Tips'
        }
        description={
          articleContent?.metaDescription
            ? `${articleContent?.metaDescription}`
            : 'Explore expert tips, trends, and insights on social casino gaming at The Money Factory Blog. Learn how to play for free, spot legit platforms, and enjoy risk-free entertainment.'
        }
        // keywords={['about us', 'company', 'mission', 'team']}
        // url={`https://www.themoneyfactory.com${location?.pathname}`}
        // imageUrl='https://testttyourdomain.com/images/about-us.jpg'
      />
      {loading ? (
        <Box
          className='maxWidthContainer'
          display='flex'
          justifyContent='center'
          alignItems='center'
          minHeight='90vh'
          width='100%'
        >
          <CircularProgress />
        </Box>
      ) : articleContent ? (
        <Grid className={classes.landingPageWrap}>
          <header>
            <LandingHeader />
          </header>

          <ArticleSchema
            slug={articleContent?.slug}
            title={articleContent?.metaTitle}
            description={articleContent.metaDescription}
            authorName='Clay Johnson'
            publishedAt={articleContent?.updatedAt}
            updatedAt={articleContent?.updatedAt}
            featuredImage={articleContent?.bannerImageUrl}
          />

          <BreadcrumbSchema
            items={[
              { name: 'Home', url: 'https://www.themoneyfactory.com' },
              { name: 'Blog', url: 'https://www.themoneyfactory.com/blog' },
              { name: 'How to Play Slots', url: `https://www.themoneyfactory.com/blog/${articleContent?.slug}` }
            ]}
          />

          <Box className={classes.articleWrapper}>
            <Grid container spacing={1}>
              <Grid item xs={12} md={8}>
                <Grid className='article-wrap'>
                  <Typography variant='h1'>{articleContent?.metaTitle}</Typography>
                  <Box className='blog-detail'>
                    <Box className='blog-user'>
                      <Box className='blog-avatar'>
                        <figure>
                          <img src={bloguser} alt='user' />
                        </figure>
                      </Box>
                      <Typography variant='h5'>Clay Johnson</Typography>
                    </Box>
                    <Typography variant='body1' className='date'>
                      {formatDateToWords(articleContent?.updatedAt)}
                    </Typography>
                  </Box>

                  {/* Main Image */}

                  <Box className='blog-img'>
                    <figure>
                      <img src={articleContent?.bannerImageUrl || DefaultBlog} alt={articleContent?.bannerImageAlt} />
                    </figure>
                  </Box>
                  {/* {articleContent?.contentBody?.map((text, index) => {
                    if (text.includes('/blogImages/')) {
                      return (
                        <Box className='blog-img' key={index}>
                          <img src={text} alt={`Image ${index}`} />
                        </Box>
                      )
                    } else {
                      return (
                        <Typography key={index} variant='body1'>
                          {htmlRenderer(text)}
                        </Typography>
                      )
                    }
                  })} */}
                  <Typography variant='body1' className='w-full'>
                    {htmlRenderer(articleContent?.contentBody)}
                    {/* {stripHtmlTags(articleContent?.contentBody)} */}
                  </Typography>
                  {articleContent?.Faqs?.length > 0 && (
                    <Typography variant='h2' style={{ marginBottom: '1rem' }}>
                      Frequently Asked Questions
                    </Typography>
                  )}
                  {articleContent?.Faqs?.map((item, index) => (
                    <Accordion
                      key={item?.faqId}
                      expanded={expanded === item?.faqId}
                      onChange={() => handleChange(item?.faqId)}
                    >
                      <AccordionSummary>
                        <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1, flexGrow: 1 }}>
                          <strong>{index + 1}.</strong>
                          {htmlRenderer(item.question)}
                          {expanded === item.faqId ? <Remove sx={{ ml: 'auto' }} /> : <Add sx={{ ml: 'auto' }} />}
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Typography>{htmlRenderer(item.answer)}</Typography>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                  <Box className='share-wrap'>
                    <Box>
                      <Typography variant='body1'>Share This:</Typography>
                      <Box className='icon-wrap'>
                        <a
                          href={`https://www.facebook.com/sharer/sharer.php?u=https://themoneyfactory.com/blog/${pathName}`}
                          target='_blank'
                          rel='noopener noreferrer'
                        >
                          <figure>
                            <img src={fbIcon} alt='Facebook' />
                          </figure>
                        </a>

                        <Link>
                          <img src={igIcon} alt='instagram' onClick={shareOnInstagram} />{' '}
                        </Link>
                        <Link>
                          <a
                            href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(currentPageURL)}`}
                            target='_blank'
                            rel='noopener noreferrer'
                          >
                            <img src={xIcon} alt='X' />
                          </a>
                        </Link>
                        <Link>
                          <a
                            href={`https://telegram.me/share/url?url=${encodeURIComponent(currentPageURL)}`}
                            target='_blank'
                            rel='noopener noreferrer'
                          >
                            <img src={telegramBlog} alt='Telegram' style={{ height: '24px' }} />
                          </a>
                        </Link>
                      </Box>
                    </Box>
                    <Box className='btn-wrap'>
                      <button>Casino Table Games</button>
                      <button>Online Table Games</button>
                    </Box>
                  </Box>
                </Grid>
              </Grid>

              <Grid item xs={12} md={4}>
                <Grid className='welcome-bonus'>
                  <Typography variant='h3'>Welcome Bonus</Typography>
                  <Box className='welcome-box' onClick={handleWelcome}>
                    <Typography variant='h2'>EXCLUSIVE OFFER FOR BLOG READERS</Typography>
                    <img src={welcomeImg} alt='welcome-img' />
                    <Typography variant='body1'>PLAY FOR FREE: TheMoneyFactory.com</Typography>
                  </Box>
                </Grid>

                <Box className='hero-blog-list'>
                  <Typography variant='h2'>Popular Articles</Typography>
                  <Box className='blog-list-wrap'>
                    {popularBlogs?.map((article) => (
                      <Box
                        className='blog-box'
                        onClick={() => handleClick(article.slug || article?.id, !!article?.slug)}
                      >
                        <Box className='img-wrap'>
                          <img src={article.image || article?.bannerImageUrl} alt='Blog' />
                        </Box>
                        <Box className='blog-content'>
                          <Typography variant='h4'>{article.name || article?.metaTitle}</Typography>
                          <Typography variant='body1'>
                            {article.publishedDate || formatDateToWords(article?.createdAt)}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>
              </Grid>
            </Grid>
            <Box className='updates-section'>
              <Typography variant='h3'>Related Articles</Typography>
              <Typography variant='body1'>
                Stay one step ahead with The Money Factory's casino updates! We're your ultimate source for all things
                gaming on our site; from new jackpots, thrilling features to providers.
              </Typography>
              <Grid container spacing={{ xs: 1, md: 2 }}>
                {relatedArticles?.map((article) => (
                  <Grid item xs={12} sm={6} lg={4}>
                    <Box className='update-blog'>
                      <img
                        src={article?.bannerImageUrl || DefaultBlog}
                        alt='blog'
                        onClick={() => handleClick(article.slug)}
                      />
                      <Typography variant='h4'>{article.metaTitle}</Typography>
                      <Box className='blog-detail'>
                        <Typography variant='body1'>The Money factory Social Casino</Typography>
                        <Typography className='blog-date' variant='body1'>
                          {article.publishedDate}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Box>
          <SeoLandingFooter />
        </Grid>
      ) : (
        <></>
      )}
    </>
  )
}

export default DynamicBlog
