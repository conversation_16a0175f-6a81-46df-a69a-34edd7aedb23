import { useState } from 'react'
import toast from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'

// import mainArticleImg from '/assets/blogImages/bestRouletteBets/1.jpg'
// import trendMainArticle from '/assets/blogImages/howToWinAtCasino/2.jpg'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'

const useBlogSection = () => {
  const navigate = useNavigate()
  const [selectedTab, setSelectedTab] = useState('popular')
  const [openShareId, setOpenShareId] = useState(null)
  const [searchOpen] = useState(false)
  const [searchText, setSearchText] = useState('')

  // ALL ARTICLES ARRAY

  const mainArticle = {
    id: 'best-roulette-bets-for-maximizing-winnings',
    name: 'Best Roulette Bets for Maximizing Your Winnings',
    // image: mainArticleImg,
    publishedDate: '1 March 2025'
  }

  const mainTrendArticle = {
    id: 'how-to-win-at-the-casino-with-$20',
    name: 'How to Win at the Casino with $20: Smart Strategies',
    // image: trendMainArticle,
    publishedDate: '6 March 2025'
  }

  const handleClick = (articleName) => {
    navigate(`/blog/${articleName}`)
  }
  const handleDynamicClick = (articleSlug) => {
    navigate(`/blog/${articleSlug}`)
  }
  const [popularBlogs, setPopularBlogs] = useState([])
  const [recentlyAddedBlogs, setRecentlyAddedBlogs] = useState([])
  const [filteredArticles, setFilteredArticles] = useState([])
  const searchArticles = [mainArticle, mainTrendArticle]

  const shareOnInstagram = (id) => {
    const currentPageURL = `https://themoneyfactory.com/blog/${id}`
    navigator.clipboard.writeText(currentPageURL).then(() => {
      toast.success('Link copied to clipboard! Now you can paste it on Instagram.')
    })
  }

  const formatDateToWords = (isoDateString) => {
    const date = new Date(isoDateString)
    const day = date.getDate()
    const month = date.toLocaleString('default', { month: 'long' })
    const year = date.getFullYear()

    return `${day} ${month} ${year}`
  }

  const handleOnChange = (event) => {
    const text = event.target.value
    setSearchText(text)
    if (text.trim() === '') {
      setFilteredArticles([])
      setSearchText('')
    } else {
      setFilteredArticles(
        recentlyAddedBlogs.filter((article) => article.name.toLowerCase().includes(text.toLowerCase()))
      )
    }
  }



  function getRelatedArticles(myHeading, blogs) {
    if (!Array.isArray(blogs) || !myHeading) return [];

    // Filter out the current article
    const filteredArticles = blogs.filter(article => article.name !== myHeading);

    // Break the heading into words
    const headingWords = myHeading?.toLowerCase()?.split(/\s+/);

    // Find articles that match any of the heading words
    let relatedArticles = filteredArticles.filter(article =>
      headingWords.some(word =>
        article?.name?.toLowerCase()?.includes(word)
      )
    );

    // Fill in with random articles if we don't have 3 yet
    while (relatedArticles.length < 3 && filteredArticles.length > relatedArticles.length) {
      const randomArticle = filteredArticles[Math.floor(Math.random() * filteredArticles.length)];
      if (!relatedArticles.includes(randomArticle)) {
        relatedArticles.push(randomArticle);
      }
    }

    // Shuffle and return the top 3
    return relatedArticles.slice(0, 3)
  }



  return {
    selectedTab,
    setSelectedTab,
    openShareId,
    setOpenShareId,
    searchOpen,
    searchText,
    setSearchText,
    handleClick,
    filteredArticles,
    setFilteredArticles,
    searchArticles,
    shareOnInstagram,
    handleOnChange,
    mainArticle,
    mainTrendArticle,
    getRelatedArticles,
    handleDynamicClick,
    formatDateToWords,
    popularBlogs,
    setPopularBlogs,
    recentlyAddedBlogs,
    setRecentlyAddedBlogs,
  }
}

export default useBlogSection
