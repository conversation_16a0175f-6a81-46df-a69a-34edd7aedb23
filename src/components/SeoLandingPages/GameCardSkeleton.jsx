import React from 'react'
import { Box } from '@mui/material'
import { AspectRatios, getImageStyleProps } from '../../utils/imageUtils'

/**
 * Skeleton loader for game cards to prevent layout shifts
 * Maintains consistent aspect ratio and dimensions
 */
const GameCardSkeleton = ({ count = 7 }) => {
  const skeletonStyle = getImageStyleProps({
    aspectRatio: AspectRatios.GAME_CARD, // Now matches 333/470 ratio
    width: '100%',
    height: 'auto',
    objectFit: 'contain',
    additionalStyles: {
      backgroundColor: '#1a1a1a',
      borderRadius: '0.5rem',
      display: 'block',
      position: 'relative',
      overflow: 'hidden',
      '&::after': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: '-100%',
        width: '100%',
        height: '100%',
        background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent)',
        animation: 'shimmer 1.5s infinite'
      }
    }
  })

  return (
    <>
      <style>
        {`
          @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
          }
        `}
      </style>
      {Array.from({ length: count }, (_, index) => (
        <Box
          key={`skeleton-${index}`}
          sx={skeletonStyle}
          aria-label="Loading game card"
        />
      ))}
    </>
  )
}

export default GameCardSkeleton
