import React, { useRef, useEffect, useState, useCallback, memo } from 'react'
import useStyles from './styles'
import { Grid, Box, Typography, Button, Stack } from '@mui/material'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'
import 'swiper/css/pagination'
import { fist } from '../ui-kit/icons/svg'
import SeoLandingFooter from './SeoLandingFooter'
import casinoQuery from '../../reactQuery/casinoQuery'
import { usePortalStore } from '../../store/store'
import Signup from '../Modal/Signup'
import { CasinoQuery } from '../../reactQuery'
import { debounce } from 'lodash'
import { PlayerRoutes } from '../../routes'
import LandingHeader from '../../pages/Landing/LandingHeader'
import SeoHead from '../../utils/seoHead'
import useDynamicGamepageStore from '../../store/useDynamicGamepageStore'
import useDynamicGamePageQuery from '../../reactQuery/useDynamicGamePageQuery'
import { Link } from 'react-router-dom'
import LazyImage from '../../utils/lazyImage'
import { AspectRatios, getImageStyleProps } from '../../utils/imageUtils'
import GameCardSkeleton from './GameCardSkeleton'

// Memoized GameCard component to prevent unnecessary re-renders
const GameCard = memo(({ image, index, onClick, isEager = false }) => (
  <LazyImage
    key={`game-${index}`}
    src={image.imageUrl || fist}
    alt={image.name || `Game Card ${index + 1}`}
    onClick={onClick}
    lazy={!isEager} // Load first row eagerly for LCP
    style={getImageStyleProps({
      aspectRatio: AspectRatios.GAME_CARD,
      width: '100%',
      height: 'auto',
      objectFit: 'contain', // Use contain to prevent distortion of game cards
      additionalStyles: {
        borderRadius: '0.5rem',
        cursor: 'pointer',
        display: 'block'
      }
    })}
    loaderStyles={getImageStyleProps({
      aspectRatio: AspectRatios.GAME_CARD,
      width: '100%',
      height: 'auto',
      additionalStyles: {
        backgroundColor: '#1a1a1a',
        borderRadius: '0.5rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }
    })}
    fallback={fist}
    placeholder={fist}
  />
))

GameCard.displayName = 'GameCard'

const GamesPage = () => {
  const portalStore = usePortalStore()
  const classes = useStyles()
  useDynamicGamePageQuery()
  const [selectedCategory, setSelectedCategory] = useState(1)
  const { data: subCategories, isLoading, refetch } = casinoQuery.getSubcategoryListQuery({ params: {} })
  const dynamicGamePageData = useDynamicGamepageStore((state) => state.allGamePages)
  const swiperRef = useRef(null)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [gameData, setGameData] = useState([])
  const successToggler = (data) => {
    setGameData(data?.data?.data?.rows)
  }
  const errorToggler = (error) => {
    console.log(error)
  }
  const gameListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })

  // Performance monitoring for LCP optimization
  useEffect(() => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slideTo(0)
    }

    // Mark when games section is ready for performance monitoring
    if (typeof window !== 'undefined' && window.performance) {
      window.performance.mark('games-section-ready')
    }
  }, [])

  useEffect(() => {
    if (!isLoading && subCategories?.data) {
      const data = subCategories?.data?.[0]
      setSelectedCategory(data)
    }
  }, [isLoading])

  const handleGameClick = () => {
    portalStore.openPortal(() => <Signup />, 'loginModal')
  }

  const debouncedSetSearch = useCallback(
    debounce((searchQuery) => {
      const trimmedQuery = searchQuery.trim()
      setDebouncedSearch(trimmedQuery)
    }, 300),
    []
  )

  useEffect(() => {
    debouncedSetSearch(search)
  }, [search, debouncedSetSearch])

  useEffect(() => {
    if (debouncedSearch !== '') {
      const filterData = { subCategoryId: 0, search: debouncedSearch }
      gameListMutation.mutate(filterData)
    }
  }, [debouncedSearch])

  useEffect(() => {
    if (search === '') {
      setGameData([])
    }
  }, [search])

  const casinoButtons = [
    // {
    //   label: 'Jackpot Casino',
    //   url: PlayerRoutes.jackpotSeo
    // },
    {
      label: 'Social Slot Casino',
      url: '/games/social-slot-games'
    },
    {
      label: 'Casino Table Games',
      url: '/games/casino-table-games'
    },
    {
      label: 'Scratch Card Games',
      url: '/games/online-casino-scratch-card-games'
    },
    {
      label: 'Popular Casino Games',
      url: '/games/popular-casino-games'
    },
    {
      label: 'Live Dealer Casino Games',
      url: '/games/live-dealer-casino-games'
    },
    {
      label: 'Instant Win Casino Games',
      url: '/games/instant-win-casino-games'
    },
    {
      label: 'Slingo Casino Games',
      url: '/games/slingo-casino-games'
    }
  ]
  return (
    <>
      {/* <Helmet>
        <title> Explore the Thrill of Social Casino Games and Win Big</title>
        <meta
          name='description'
          content='Dive into the world of social casino games, where fun meets winning! Play with friends, enjoy classic casino experiences, and compete for exciting rewards'
        />
        <meta name='robots' content='index, follow' />
      </Helmet> */}
      <SeoHead
        title='Explore the Thrill of Social Casino Games and Win Big'
        description='Dive into the world of social casino games, where fun meets winning! Play with friends, enjoy classic casino experiences, and compete for exciting rewards'
        keywords={['social casino games', 'online casino', 'slots', 'table games', 'sweepstakes', 'free games']}
      />
      {/* Critical resource hints for performance optimization */}
      <link rel='preload' as='image' href={fist} fetchPriority='high' />
      <link rel='preconnect' href='https://fonts.googleapis.com' />
      <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='anonymous' />
      <link rel='dns-prefetch' href='//www.themoneyfactory.com' />
      <link rel='dns-prefetch' href='//www.facebook.com' />
      <link rel='dns-prefetch' href='//www.instagram.com' />
      <main className={classes.landingPageWrap}>
        <>
          <header>
            <LandingHeader />
          </header>
          <section className='games-page-wrap'>
            <h1>
              <span>Explore All Casino Games – </span>Slots, Sweepstakes, And Table Games
            </h1>
            <nav>
              <Stack direction='row' className='btn-section-content' spacing={2}>
                {casinoButtons.map((btn, index) => (
                  <Button
                    key={index}
                    onClick={() => window.open(btn.url, '_blank', 'noopener,noreferrer')}
                    className='btn btn-primary'
                  >
                    {btn.label}
                  </Button>
                ))}
                {/* <Stack direction='row' className='btn-section-content' spacing={2}> */}
                {dynamicGamePageData?.dynamicGameDetails?.map((btn, index) => (
                  <Button
                    key={index}
                    onClick={() => window.open(`games/${btn.slug}`, '_blank', 'noopener,noreferrer')}
                    className='btn btn-primary'
                  >
                    {btn.title}
                  </Button>
                ))}
                {/* </Stack> */}
              </Stack>
            </nav>

            <section className='game-card-grid-wrap'>
              {isLoading ? (
                <GameCardSkeleton count={21} />
              ) : gameData?.length > 0 ? (
                gameData.map((image, index) => (
                  <GameCard
                    key={`game-${image.id || index}`}
                    image={image}
                    index={index}
                    onClick={handleGameClick}
                    isEager={index < 7} // Load first row eagerly for LCP
                  />
                ))
              ) : selectedCategory?.subCategoryGames ? (
                selectedCategory.subCategoryGames.map((image, index) => (
                  <GameCard
                    key={`category-game-${image.id || index}`}
                    image={image}
                    index={index}
                    onClick={handleGameClick}
                    isEager={index < 7} // Load first row eagerly for LCP
                  />
                ))
              ) : (
                <GameCardSkeleton count={21} />
              )}
            </section>

            <Grid className='game-section-content'>
              <Typography variant='body1'>
                Welcome to{' '}
                <Link to='https://www.themoneyfactory.com/' target='_blank'>
                  {' '}
                  The Money Factory
                </Link>
                , your go-to destination for free, fun, and fast-paced{' '}
                <Link to='/games/online-social-casino-games' target='_blank'>
                  social casino games
                </Link>
                . No downloads. No deposits. Just non-stop entertainment.
              </Typography>
              <Typography variant='body1' className='space-down'>
                Whether you're into spinning slots, chasing jackpots, or testing your luck with table games, there's
                something here for everyone.
              </Typography>
              <Typography variant='h2'>
                Discover a Wide Range of{' '}
                <Typography variant='span' className='yellow-text'>
                  {' '}
                  Sweepstakes Games
                </Typography>{' '}
              </Typography>
              <Typography variant='body1' className='space-down'>
                We’ve got all the favorites — from{' '}
                <Link to='/games/instant-win-casino-games' target='_blank'>
                  instant win games
                </Link>{' '}
                and{' '}
                <Link to='/games/jackpot-casino-games' target='_blank'>
                  jackpot
                </Link>{' '}
                slots to classic table and specialty games. Every game is 100% free to play and designed to keep you
                entertained, no matter your style.
              </Typography>
              <Typography variant='body1' className='space-down'>
                With high-quality graphics and smooth gameplay from top developers, you’ll enjoy the same thrills of a
                real casino, without spending a dime.
              </Typography>
            </Grid>
            <Grid container mb={2} spacing={1}>
              <Grid item xs={12} md={6}>
                <article className='game-content-box'>
                  <Typography variant='h3'>
                    <Typography variant='span' className='yellow-text'>
                      Top Slot Games
                    </Typography>{' '}
                    to Play Online for Free
                  </Typography>
                  <Typography variant='body1'>
                    Looking for exciting spins and big virtual wins? Dive into our most popular free slots:
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Progressive jackpots
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Classic fruit machine vibes
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Bonus-filled video slots
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Daily free spins
                  </Typography>
                  <Typography variant='body1'>Fan-favorite titles include:</Typography>
                  <Typography variant='body1' className='pointer'>
                    Mega Gold Jackpot
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Fire Frenzy Slots
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Reels of Mystery
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Fortune Spin Deluxe
                  </Typography>
                </article>
              </Grid>
              <Grid item xs={12} md={6}>
                <article className='game-content-box'>
                  <Typography variant='h3'>
                    <Typography variant='span' className='yellow-text'>
                      Instant Win Games
                    </Typography>{' '}
                    are Quick, Easy, and Fun
                  </Typography>
                  <Typography variant='body1' className='space-down'>
                    Our{' '}
                    <Link to='/games/instant-win-casino-games' target='_blank'>
                      instant win games
                    </Link>{' '}
                    are perfect when you want fast action. Pick a card, tap a box, or spin the wheel and see if you’ve
                    won right away.
                  </Typography>
                  <Typography variant='body1'>Why players love them:</Typography>
                  <Typography variant='body1' className='pointer'>
                    Easy to play
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Instant results
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Great for short sessions
                  </Typography>
                  <Typography variant='body1' className='space-down'>
                    Try your luck, it only takes a few seconds.
                  </Typography>
                  <Typography variant='body1'>
                    No download or sign-up required —{' '}
                    <Link to='/games/social-slot-games' target='_blank'>
                      just pick a game and start spinning.
                    </Link>
                  </Typography>
                </article>
              </Grid>
              <Grid item xs={12} md={6}>
                <article className='game-content-box'>
                  <Typography variant='h3'>
                    <Typography variant='span' className='yellow-text'>
                      Free Table & Card Games
                    </Typography>{' '}
                    and No Deposit Needed
                  </Typography>
                  <Typography variant='body1'>
                    If you enjoy games that test your skill, explore our{' '}
                    <Link to='/games/casino-table-games' target='_blank'>
                      free table
                    </Link>{' '}
                    and{' '}
                    <Link to='/games/online-casino-scratch-card-games' target='_blank'>
                      card games
                    </Link>
                    :
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Blackjack
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Roulette
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Jacks or Better
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Joker Poker
                  </Typography>
                  <Typography variant='body1'>
                    These casino classics combine strategy and luck for a more engaging experience, all free to play,
                    with no risk involved.
                  </Typography>
                </article>
              </Grid>
              <Grid item xs={12} md={6}>
                <article className='game-content-box'>
                  <Typography variant='h3'>
                    <Typography variant='span' className='yellow-text'>
                      Play with No Deposit, No Download —
                    </Typography>{' '}
                    Just Rewards
                  </Typography>
                  <Typography variant='body1'>
                    <Link to='https://www.themoneyfactory.com/' target='_blank'>
                      The Money Factory
                    </Link>{' '}
                    runs on a social casino model, which means{' '}
                    <Typography variant='span' className='yellow-text'>
                      zero cost to play
                    </Typography>
                    , real-time rewards, and non-stop fun.
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Daily login bonuses
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Earn virtual coins as you play
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    100% free-to-play experience
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Safe and secure, no real money gambling
                  </Typography>
                  <Typography variant='body1'>
                    Sign up in seconds, claim your welcome bonus, and start exploring hundreds of games.
                  </Typography>
                </article>
              </Grid>
              <Grid item xs={12}>
                <article className='game-content-box'>
                  <Typography variant='h3'>
                    <Typography variant='span' className='yellow-text'>
                      Exclusive Games
                    </Typography>{' '}
                    You Won’t Find Anywhere Else
                  </Typography>
                  <Typography variant='body1'>
                    Want something unique? Check out our original titles, crafted in-house to deliver fun you can’t get
                    on any other platform.
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Creative gameplay
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Special features and rewards
                  </Typography>
                  <Typography variant='body1' className='pointer'>
                    Fresh updates and limited-edition games
                  </Typography>
                  <Typography variant='body1'>
                    We’re always adding new options to keep your experience exciting.
                  </Typography>
                </article>
              </Grid>
            </Grid>
          </section>
          <footer>
            <SeoLandingFooter />
          </footer>
        </>
      </main>
    </>
  )
}

// Memoize the main component to prevent unnecessary re-renders
export default memo(GamesPage)
