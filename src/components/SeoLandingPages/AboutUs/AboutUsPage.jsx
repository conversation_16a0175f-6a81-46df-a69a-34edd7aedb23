import React, { lazy, Suspense } from 'react'

import { Grid, Box, Typography, Link } from '@mui/material'
import useStyles from './style'

import playAnywhere from '../../../components/ui-kit/icons/svg/play-anywhere.svg'
import customerSupport from '../../../components/ui-kit/icons/svg/customer-support.svg'
import joinUs from '../../../components/ui-kit/icons/svg/join-us.svg'
import { AboutUs1, AboutUs } from '../../ui-kit/icons/opImages'
import LandingHeader from '../../../pages/Landing/LandingHeader'
import SeoHead from '../../../utils/seoHead'
import LazyImage from '../../../utils/lazyImage'

// Lazy load non-critical components
const SeoLandingFooter = lazy(() => import('../SeoLandingFooter'))

const AboutUsPage = () => {
  const classes = useStyles()

  return (
    <Grid style={{ width: '100%' }}>
      <SeoHead
        title='About Us - The Money Factory | Social Casino Games'
        description='Learn about The Money Factory - your premier destination for social casino games. Discover our mission, team, and commitment to providing the best online gaming experience.'
        keywords={['about us', 'company', 'mission', 'team', 'social casino', 'the money factory']}
        imageUrl={AboutUs}
      /> 
      {/* Critical resource hints for LCP optimization */}
      <link rel="preload" as="image" href={AboutUs} fetchPriority="high" />
      <link rel="preload" as="image" href={AboutUs1} fetchPriority="low" />
      <link rel="preload" as="image" href={playAnywhere} fetchPriority="low" />
      <link rel="preload" as="image" href={customerSupport} fetchPriority="low" />
      <link rel="preload" as="image" href={joinUs} fetchPriority="low" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="//www.themoneyfactory.com" />
      <link rel="dns-prefetch" href="//www.facebook.com" />
      <link rel="dns-prefetch" href="//www.instagram.com" />
      <LandingHeader />
      <Box className={classes.aboutusWrapper}>
        <Box className='bannerWrap'>
          <Box className='bannerContent'>
            <Typography variant='h3'>About Us</Typography>
            <Typography variant='h5'>Welcome to The Money Factory</Typography>
            <Typography variant='body1'>
              At{' '}
              <Link
                href='https://www.themoneyfactory.com/'
                target='_blank'
                style={{ color: '#fff', textDecorationColor: '#fff' }}
              >
                The Money Factory
              </Link>{' '}
              we’re not just another online casino site. We’re a team of real people who love casino games and got tired
              of boring, corporate-run platforms. We created this space to bring back the fun, fairness, and connection
              that’s often missing in online gaming.
            </Typography>
            <Typography variant='body1'>
              We know the frustration — confusing rules, paywalls, fake rewards. That’s why we’re doing things
              differently.
            </Typography>
          </Box>
        </Box>
        <Box className='aboutus-content'>
          <Typography variant='h2'>
            <Typography variant='span'>What We Offer:</Typography> Real Casino Vibes, No Real Money
          </Typography>
          <Typography variant='body1' className='heading-p'>
            We hand-pick only the{' '}
            <Link
              style={{ color: '#fff', textDecorationColor: '#fff' }}
              href='/games'
              target='_blank'
            >
              most popular and exciting casino games
            </Link>
            , the ones players actually want to play.
          </Typography>
          <Grid container spacing={{ xs: 1, md: 3 }}>
            <Grid item xs={12} md={6}>
              <Typography variant='h3' className='heading-h3'>
                Featured Slot Games:
              </Typography>
              <Typography variant='body1' className='pointer'>
                <Typography variant='span' className='yellow-text'>
                  Book of Dead
                </Typography>{' '}
                – Classic feel with thrilling expanding symbols
              </Typography>
              <Typography variant='body1' className='pointer'>
                <Typography variant='span' className='yellow-text'>
                  Gates of Olympus
                </Typography>{' '}
                – High-volatility multipliers that stack fast
              </Typography>
              <Typography variant='body1' className='pointer'>
                <Typography variant='span' className='yellow-text'>
                  Starburst
                </Typography>{' '}
                – Smooth gameplay with frequent small wins
              </Typography>
              <Typography variant='body1' className='pointer space-down'>
                <Typography variant='span' className='yellow-text'>
                  Dead or Alive II
                </Typography>{' '}
                – Huge potential in the bonus rounds
              </Typography>
              <Typography variant='h3' className='heading-h3'>
                Classic Table Games:
              </Typography>
              <Typography variant='body1' className='pointer'>
                <Typography variant='span' className='yellow-text'>
                  Blackjack
                </Typography>{' '}
                – Real strategy, real odds
              </Typography>
              <Typography variant='body1' className='pointer space-down'>
                <Typography variant='span' className='yellow-text '>
                  Craps
                </Typography>{' '}
                – Includes odds bets, the smartest wager in the house
              </Typography>
              <Typography variant='body1'> We’re all about quality, not filler. </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box className='about-img'>
                <LazyImage
                  src={AboutUs1}
                  alt='The Money Factory social casino games experience'
                  lazy={true}
                  threshold={0.2}
                  style={{
                    height: '100%',
                    width: '100%',
                    objectFit: 'cover',
                    objectPosition: 'center',
                    display: 'block'
                  }}
                  loaderStyles={{
                    height: '100%',
                    width: '100%',
                    backgroundColor: '#1a1a1a',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={6} sx={{ order: { xs: 2, md: 1 } }}>
              <Typography variant='h2' className='pointer space-down'>
                <Typography variant='span' className='yellow-text'>
                  Free Coins, Real Rewards –
                </Typography>{' '}
                Every Single Day
              </Typography>
              <Typography variant='body1' className='firstP'>No tricky wagering requirements. No bait-and-switch. Just:</Typography>
              <Typography variant='body1' className='pointer white-pointer'>
                Daily free coins
              </Typography>
              <Typography variant='body1' className='pointer white-pointer'>
                Progressive bonuses for loyal players
              </Typography>
              <Typography variant='body1' className='pointer white-pointer space-down'>
                VIP perks for our most dedicated members
              </Typography>
              <Typography variant='body1'>
                Every spin, bet, and win helps you unlock more. More playtime, more excitement, more fun.
              </Typography>
            </Grid>
            <Grid item xs={12} md={6} sx={{ order: { xs: 1, md: 2 } }}>
              <Typography variant='h2' className='pointer space-down'>
                <Typography variant='span' className='yellow-text'>
                  It’s More
                </Typography>{' '}
                Fun With Friends
              </Typography>
              <Typography variant='body1' className='firstP'>The Money Factory was built for social gaming:</Typography>
              <Typography variant='body1' className='pointer white-pointer'>
                Connect with friends
              </Typography>
              <Typography variant='body1' className='pointer white-pointer'>
                Challenge each other
              </Typography>
              <Typography variant='body1' className='pointer white-pointer'>
                Share your biggest wins
              </Typography>
              <Typography variant='body1' className='pointer white-pointer'>
                Climb our weekly leaderboards
              </Typography>
              <Typography variant='body1' className='pointer white-pointer space-down'>
                Join tournaments and compete against real players
              </Typography>
              <Typography variant='body1'>This is more than a game — it’s a community.</Typography>
            </Grid>
          </Grid>
          <Box className='info-section'>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box className='info-box border-left'>
                  <Box className='info-heading'>
                    <img src={playAnywhere} alt='play-anywhere' />
                    <Typography variant='h4'>Play Anytime, Anywhere</Typography>
                  </Box>
                  <Typography variant='body1'>
                    Your experience should follow you wherever you go. That’s why{' '}
                    <Link href='https://www.themoneyfactory.com/' target='_blank'>
                      The Money Factory
                    </Link>{' '}
                    works seamlessly on mobile, tablet, and desktop. No downloads. No lag. Just smooth gameplay at home
                    or on the go.
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box className='info-box border-left'>
                  <Box className='info-heading'>
                    <img src={customerSupport} alt='play-anywhere' />
                    <Typography variant='h4'>Got a Question? We're Here 24/7</Typography>
                  </Box>
                  <Typography variant='body1'>
                    <Link href='/contact-us' target='_blank'>
                      Our support team
                    </Link>{' '}
                    is ready to help – any time, any day. Whether it’s understanding game rules or sorting out a glitch,
                    we respond fast.
                  </Typography>
                  <Typography variant='body1'>
                    - Typical reply time: Under 2 hours
                  </Typography>
                  <Typography variant='body1'>- Real people. Real support</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box className='info-box'>
                  <Box className='info-heading'>
                    <img src={joinUs} alt='play-anywhere' />
                    <Typography variant='h4'>Join The Money Factory – It’s Free</Typography>
                  </Box>
                  <Typography variant='body1'>
                    <Link href='/games' target='_blank'>
                      Ready to experience casino gaming
                    </Link>{' '}
                    that’s fun, fair, and fully social? Join The Money Factory today and get your first free coins to
                    start spinning.
                  </Typography>
                  <Typography variant='body1'>No deposits. No risk. Just pure entertainment.</Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Box>
      <Suspense fallback={
        <Box style={{
          height: '300px',
          backgroundColor: '#1a1a1a',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#666',
          fontSize: '14px'
        }}>
          Loading footer...
        </Box>
      }>
        <SeoLandingFooter />
      </Suspense>
    </Grid>
  )
}

export default AboutUsPage
