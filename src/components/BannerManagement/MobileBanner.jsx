import { lazy, Suspense } from 'react'
import { Box, Grid, Typography } from '@mui/material'
import { useUserStore } from '../../store/useUserSlice'
import { useNavigate } from 'react-router-dom'
import { usePortalStore } from '../../store/store'
import { getLoginToken } from '../../utils/storageUtils'
import { getBannerImageStyles } from '../../utils/imageUtils'
import { mobileDefaultBanner } from '../ui-kit/icons/banner'

const Signup = lazy(() => import('../Modal/Signup'))
const CountDownTimer = lazy(() => import('../CountDownTimer'))

const MobileBanner = ({ info, index }) => {
  const auth = useUserStore((state) => state)
  const navigate = useNavigate()

  const portalStore = usePortalStore((state) => state)
  const handleRedirection = (key) => {
    if (getLoginToken() || auth?.isAuthenticate) {
      if (key !== '') navigate(`/${key}`)
    } else {
      portalStore.openPortal(
        () => (
          <Suspense fallback={null}>
            <Signup />
          </Suspense>
        ),
        'loginModal'
      )
    }
  }

  const showCountdown = info.isCountDown && new Date(info?.endDate) > new Date(info?.startDate)

  return (
    <Grid>
      <Grid
        onClick={() => handleRedirection(info?.navigateRoute)}
        role='button'
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            handleRedirection(info?.navigateRoute)
          }
        }}
        sx={{
          cursor: 'pointer',
          transition: 'transform 0.2s ease',
          '&:hover': {
            transform: 'scale(1.02)'
          },
          '&:focus': {
            outline: '2px solid #FDB72E',
            outlineOffset: '2px'
          }
        }}
      >
        <img
          src={info?.mobileBannerImage || mobileDefaultBanner}
          alt={info.textOne || 'Banner Image'}
          loading={index === 0 ? 'eager' : 'lazy'} // Load first banner eagerly for LCP
          fetchPriority={index === 0 ? 'high' : 'low'} // Prioritize first banner
          style={getBannerImageStyles({
            isMobile: true,
            additionalStyles: {
              maxWidth: '100%' // Remove maxWidth constraint for better layout
            }
          })}
          decoding="async"
        />
        <Grid className='bannerTextLobbySlider'>
          <Box>
            <Typography>
              {showCountdown && (
                <Suspense fallback={null}>
                  <CountDownTimer eventDateTime={info.endDate} />
                </Suspense>
              )}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default MobileBanner
