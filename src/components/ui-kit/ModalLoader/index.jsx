import React from 'react'
import { Box, CircularProgress } from '@mui/material'

/**
 * Lightweight modal loader component for Suspense fallbacks
 * Optimized for modal loading states with minimal bundle size
 */
const ModalLoader = () => (
  // <Box
  //   display="flex"
  //   justifyContent="center"
  //   alignItems="center"
  //   minHeight="200px"
  //   width="100%"
  // >
    <CircularProgress 
      size={40}
      thickness={4}
      sx={{ 
        color: '#FDB72E' // Match your brand color
      }}
    />
  // </Box>
)

export default ModalLoader
