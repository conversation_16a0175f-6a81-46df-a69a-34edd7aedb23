import React, { useEffect, memo, useMemo } from 'react'
import { Grid, Typography, Box } from '@mui/material'
import LazyImage from '../../utils/lazyImage'
import { getImageStyleProps, AspectRatios } from '../../utils/imageUtils'
import Day2 from '../../components/ui-kit/icons/png/streak-day-2.png'
import Day4 from '../../components/ui-kit/icons/png/streak-day-4.png'
import Day5 from '../../components/ui-kit/icons/png/streak-day-5.png'
import { loadLottieScript } from '../../utils/loadLottieScript'
/* eslint-disable multiline-ternary */

const DayBox = memo(({ data, enableAnimation, claimDailyBonus }) => {
  const dayClass = `day-${data?.day}`
  const isDay7 = data?.day === 7

  const formatNumber = (num) =>
    num >= 1000000 ? `${(num / 1000000).toFixed(1)}M` : num >= 1000 ? `${(num / 1000).toFixed(1)}K` : num

  // Memoized image styles for consistent aspect ratios
  const imageStyles = useMemo(() =>
    getImageStyleProps({
      aspectRatio: AspectRatios.SQUARE,
      width: '54px',
      height: '54px',
      objectFit: 'contain',
      additionalStyles: {
        filter: 'drop-shadow(0px 5.04px 5.04px #00000066)'
      }
    }), []
  )

  const rewardImageStyles = useMemo(() =>
    getImageStyleProps({
      aspectRatio: AspectRatios.SQUARE,
      width: '56px',
      height: '56px',
      objectFit: 'contain'
    }), []
  )

  useEffect(() => {
    loadLottieScript()
  }, [])

  return (
    <Grid item xs={isDay7 ? 12 : 4} md={isDay7 ? 6 : 3} key={data?.bonusId}>
      <Grid
        className={`streak-box ${dayClass}`}
        onClick={() => {
          data.isClaimableToday && claimDailyBonus(data?.bonusId)
        }}
      >
        <Typography variant='body1'>Day {data?.day}</Typography>

        {isDay7 ? (
          <Box className='reward-wrap'>
            <LazyImage
              src={Day2}
              alt='day-2 reward'
              style={rewardImageStyles}
              lazy={false}
            />
            <Typography>+</Typography>
            <LazyImage
              src={Day4}
              alt='day-4 reward'
              style={rewardImageStyles}
              lazy={false}
            />
            <Typography>+</Typography>
            <LazyImage
              src={Day5}
              alt='day-5 reward'
              style={rewardImageStyles}
              lazy={false}
            />
          </Box>
        ) : (
          <LazyImage
            src={data?.imageUrl}
            alt={`day-${data?.day} reward`}
            style={imageStyles}
            lazy={false}
          />
        )}
        <Typography variant='body1'>
          {!data.isAlreadyClaimed
            ? `${formatNumber(data?.gcAmount)} GC + ${formatNumber(data?.scAmount)} SC`
            : 'Claimed'}
        </Typography>

        {(!data.isClaimableToday || (enableAnimation && data.isClaimableToday)) && (
          <Box className='claimed-box'>
            {data.isAlreadyClaimed && (
              <>
                <lottie-player
                  src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/claimed.json`}
                  background='transparent'
                  speed='1'
                  autoplay={enableAnimation}
                />
              </>
            )}
          </Box>
        )}
      </Grid>
      {data.isClaimableToday && data.isAlreadyClaimed && (
        <lottie-player
          src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/popper.json`}
          background='transparent'
          speed='1'
          loop
          autoplay={enableAnimation}
        />
      )}
    </Grid>
  )
})

DayBox.displayName = 'DayBox'

export default DayBox
