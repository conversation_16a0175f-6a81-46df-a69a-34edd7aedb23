import React, { Suspense, useEffect, useRef, useState, useCallback, useMemo, lazy } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import hideLoader from './utils/hideLoader'
import { Toaster } from 'react-hot-toast'
import {
  useJackpotStore,
  usePaymentProcessStore,
  usePortalStore,
  useSiteLogoStore,
  useStateStore,
  // useSubCategoryOnLoadStore,
  useUserStore
} from './store/store'
import { GoogleOAuthProvider } from '@react-oauth/google'
import AppRouter from './App.routes'
import { GeneralQuery, PaymentQuery, useGetProfileMutation, useLogOutMutation } from './reactQuery'
import Layout from './components/Layout'
import {
  useGetDailyBonusMutation,
  useGetFreeSpinMutation,
  useGetPromotionBonusMutation,
  useGetWelcomeBonusMutation
} from './reactQuery/bonusQuery'
import {
  liveWinnerSocket,
  socket,
  tournamentLeaderboardSocket,
  tournamentEndSocket,
  maintenanceSocket,
  jackpotSocket,
  pragmaticJackpotSocket
} from './utils/socket'
import Loader from './components/Loader'
import ModalLoader from './components/ui-kit/ModalLoader'
import { setNavigate } from './utils/navigation'
import { deleteCookie, deleteRefferalCookie, getCookie, setCookie } from './utils/cookiesCollection'
import { customEvent } from './utils/optimoveHelper'
import { IdleTimer } from './utils/idleTimer/idleTimer'
import { getLoginToken } from './utils/storageUtils'
import useLogout from './hooks/useLogout'
import Maintenance from './pages/NotFound/Maintanace'
import { handleLogout } from './network/services/user.services'
import errorMessages from './network/messages/errorMessages'
import { openErrorToaster } from './network/helper/toaster.helpers'
import { usePragmaticJackpotStore } from './store/usePragmaticJackpot'

// Lazy load modal components for better performance
const DailyBonus = lazy(() => import('./components/DailyBonus'))
const WelcomeBonus = lazy(() => import('./components/WelcomeBonus'))
const UserNameModal = lazy(() => import('./components/Modal/Signup/UserNameModal'))
const PromotionBonus = lazy(() => import('./components/DailyBonus/promotionBonus'))
const TermAndCondition = lazy(() => import('./components/Modal/TermAndCondition/TermAndCondition'))
const IdleScreen = lazy(() => import('./components/IdleScreen/IdleScreen'))
const BasicModal = lazy(() => import('./pages/NotFound/MaintenanceLogout'))
const BetLimitPopup = lazy(() => import('./components/betLimit/betLimitPopup'))
const ScratchCardComponent = lazy(() => import('./components/ScratchCard/ScratchCardComponent'))
const FreeSpinModal = lazy(() => import('./components/FreeSpinModal/FreeSpinModal'))

const body = document.querySelector('body')
body?.setAttribute('data-theme', 'yellow')

function listener() {
  if (window.scrollY > 0) {
    document.body.classList.add('scrolled')
  } else {
    document.body.classList.remove('scrolled')
  }
}

function App() {
  const params = new URLSearchParams(window.location.search)
  const paymentMethod = params.get('paymentMethod')
  const paymentMethodFromStore = window.localStorage.getItem('paymentMethod')
  const expires = useMemo(() => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toUTCString(), []) // 30 days from now
  const navigate = useNavigate()
  const location = useLocation()

  /** Zustand Stores **/
  const auth = useUserStore((state) => state)
  const user = useUserStore((state) => state)
  const userDetail = useUserStore((state) => state.userDetails)
  const isAuthenticate = useUserStore((state) => state.isAuthenticate)
  const portalStore = usePortalStore((state) => state)

  const stateList = useStateStore((state) => state)
  // const subCategoryState = useSubCategoryOnLoadStore((state) => state)
  const logoData = useSiteLogoStore((state) => state)
  const pathCookie = getCookie('path')
  const gamePlayUrl = location.pathname.startsWith('/game-play')
  const affiliateCode = params.get('btag') || ''
  const affiliateId = params.get('affid') || ''
  const affiliatePromocode = params.get('promocode') || ''
  const restrictedUser = getCookie('restrictedZoneUser')
  const prevPathnameRef = useRef(location.pathname)
  const { logoutHandler } = useLogout()
  const MaintenanceSocketConnection = useUserStore((state) => state.MaintenanceSocketConnection)
  const walletSocketConnection = useUserStore((state) => state.walletSocketConnection)
  // JACKPOT STORE
  const { setJackpotOn, setJackpotData, setNewJackpot, setJackpotPoolAmount, setJackpotMultiplier } = useJackpotStore()

  const { setPragmaticJackpotSc, setPragmaticJackpotGc } = usePragmaticJackpotStore()

  /** Affiliate Query Params Handling **/
  if (affiliateCode || affiliateId || affiliatePromocode) {
    if (auth.isAuthenticate) {
      portalStore.closePortal()
      openErrorToaster(errorMessages.unAuthorized)
      handleLogout()
    }
    document.cookie = `affiliateCode=${affiliateCode}; expires=${expires}; domain=.themoneyfactory.com; path=/`
    document.cookie = `affiliateId=${affiliateId}; expires=${expires}; domain=.themoneyfactory.com; path=/`
    document.cookie = `affiliatePromocode=${affiliatePromocode}; expires=${expires}; domain=.themoneyfactory.com; path=/`
    deleteRefferalCookie('referralcode')
  }

  /** * ReactQuery & API Side Effects ***/

  const successStateToggler = (data) => {
    stateList.setStateList(data)
  }
  const successAccessToggler = (res) => {
    if (res?.data.status === 200) {
      localStorage.setItem('allowedUserAccess', true)
    }
  }
  const errorAccessToggler = (error) => {
    if (error.response.status === 451) {
      localStorage.setItem('allowedUserAccess', false)
    } else if (
      error?.response?.data?.data?.state === 'DECLINE' ||
      error?.response?.data?.data?.ipDetails?.vpn === true ||
      error?.response?.data?.data?.ipDetails?.web_proxy === true
    ) {
      localStorage.setItem('allowedUserAccess', false)
      // handleGeocomplyPopup(error?.response?.data?.data)
    }
  }
  const successLogoToggler = (res) => {
    if (res?.data?.data) {
      const desktopLogo = res?.data?.data.find((x) => x.key === 'LOGO_URL')?.value
      const mobileLogo = res?.data?.data.find((x) => x.key === 'MOBILE_SITE_LOGO_URL')?.value
      logoData.setDesktopLogoData(desktopLogo)
      logoData.setMobileLogoData(mobileLogo)
    }
  }
  const errorLogoToggler = (error) => {
    if (error) {
      console.log(error, 'error')
    }
  }

  GeneralQuery.getStateListQuery({ successStateToggler, params: {}, enabled: true })
  GeneralQuery.getSiteLogoQuery({ successLogoToggler, errorLogoToggler })
  const accessMutate = GeneralQuery.getAccessQuery({ successAccessToggler, errorAccessToggler })
  // const {
  //   data: subCategories,
  //   isLoading,
  //   refetch
  // } = CasinoQuery.getSubcategoryListQuery({
  //   params: {}
  // })
  const { data: maintenanceApiData, isLoading: maintenanceLoading } = GeneralQuery.getMaintenanceModeQuery()

  /** * Bonus Mutations ***/
  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      if (res?.data?.data) {
        const resetData = res?.data?.data?.remainingTime
        if (isAuthenticate) {
          portalStore.openPortal(
            () => (
              <Suspense fallback={<ModalLoader />}>
                <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />
              </Suspense>
            ),
            'bonusStreak'
          )
        }
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetWelcomeBonus = useGetWelcomeBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <WelcomeBonus welcomeBonusData={res?.data?.data} />
            </Suspense>
          ),
          'bonusModal'
        )
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetPromotionBonus = useGetPromotionBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <PromotionBonus promotionBonusData={res?.data?.data} />
            </Suspense>
          ),
          'bonusModal'
        )
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })
  const mutationGetFreeSpin = useGetFreeSpinMutation({
    onSuccess: (res) => {
      if (res?.data?.freeSpinBonus.length > 0) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <FreeSpinModal data={res?.data?.freeSpinBonus} />
            </Suspense>
          ),
          'freeSpinModal'
        )
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const logOutMutation = useLogOutMutation({
    onSuccess: () => {
      logoutHandler()
      accessMutate.mutate()
    },
    onError: (error) => {
      console.error('error', error)
    }
  })

  /** * Profile & Jackpot ***/
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      const userData = res?.data?.data
      if (userData) {
        user.setUserDetails(userData)
        user.setIsAuthenticate(true)
        setJackpotOn(userData?.isJackpotOptedIn)
        setJackpotMultiplier(userData?.jackpotMultiplier)

        if (!userData.isEmailVerified && !userData?.phoneVerified) {
          user.logout()
        }

        if (
          paymentMethod !== 'SKRILL' &&
          paymentMethod !== 'PAY_BY_BANK' &&
          paymentMethod !== 'TRUSTLY' &&
          paymentMethodFromStore !== 'TRUSTLY'
        ) {
          if (pathCookie) {
            if (userData?.isPromotionBonusAllowed && !userData?.promotionBonusClaimedAt) {
              if (isAuthenticate) {
                mutationGetPromotionBonus.mutate()
              }
            } else if (!userData?.isTermsAccepted) {
              if (isAuthenticate) {
                portalStore.openPortal(
                  () => (
                    <Suspense fallback={<ModalLoader />}>
                      <TermAndCondition />
                    </Suspense>
                  ),
                  'termsNConditionModal'
                )
              }
            } else if (userData?.isWelcomeBonusAllowed && !userData?.isWelcomeBonusClaimed) {
              if (isAuthenticate) {
                mutationGetWelcomeBonus.mutate()
              }
            } else if (!gamePlayUrl && userData?.isDailyBonusAllowed && !userData?.isDailyBonusClaimed) {
              if (isAuthenticate) {
                mutationGetDailyBonus.mutate()
              }
            } else if (!userData?.isScratchCardBonusClaimed) {
              if (isAuthenticate) {
                portalStore.openPortal(
                  () => (
                    <Suspense fallback={<ModalLoader />}>
                      <ScratchCardComponent
                        scratchCardBonus={userData?.scratchCardBonusData?.scratchCardBonus}
                        userBonusId={userData?.scratchCardBonusData?.userBonusId}
                        rewardType={userData?.scratchCardBonusData?.rewardType}
                      />
                    </Suspense>
                  ),
                  'bonusStreak'
                )
              }
            } else if (userData?.isFreeSpinBonusApplicable) {
              if (isAuthenticate) {
                mutationGetFreeSpin.mutate()
              }
            }
          }
        }
      }
    },
    onError: (error) => {
      if (error && error?.response?.data?.errors?.[0]?.name === 'UserNameDoesNotExistError') {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <UserNameModal />
            </Suspense>
          ),
          'loginModal'
        )
      }
      console.log('error', error)
    }
  })

  // We'll track in a ref whether we've already "connected" sockets once during this login session.
  const hasConnectedSocketsRef = useRef(false)

  // LISTEN for "LIMIT_REACHED"
  const handleLimitReached = (data) => {
    if (data?.data?.limitReached && (!!getLoginToken() || useUserStore.getState().isAuthenticate)) {
      usePortalStore.getState().openPortal(
        () => (
          <Suspense fallback={<ModalLoader />}>
            <BetLimitPopup message={data?.data?.message} />
          </Suspense>
        ),
        'Bet Limit Popup'
      )
    }
  }

  // 3) Listen for “MULTI_LOGIN” only when walletSocketConnection is true:
  const onMultiLogin = useCallback((multiLoginData) => {
    usePortalStore.getState().openPortal(
      () => (
        <Suspense fallback={<ModalLoader />}>
          <IdleScreen message={multiLoginData.data.message} />
        </Suspense>
      ),
      'idleModal'
    )
    const transactionId = window.localStorage.getItem('transactionId')
    if (transactionId) {
      PaymentQuery.cancelDepositMutation({
        onSuccess: () => {
          usePaymentProcessStore.getState().setCancelDeposit(false)
          window.localStorage.removeItem('transactionId')
          window.localStorage.removeItem('paymentMethod')
        },
        onError: () => {
          usePaymentProcessStore.getState().setCancelDeposit(false)
        }
      }).mutate({ transactionId })
    }
    useUserStore.getState().logout()
  }, [])

  // 5) OPTIONAL: IdleTimer binding/unbinding:
  const idletimer = new IdleTimer(
    () => {
      if (getLoginToken()) {
        idletimer.deactivate()
        const transactionId = window.localStorage.getItem('transactionId')
        if (transactionId) {
          PaymentQuery.cancelDepositMutation({
            onSuccess: () => {
              usePaymentProcessStore.getState().setCancelDeposit(false)
              window.localStorage.removeItem('transactionId')
              window.localStorage.removeItem('paymentMethod')
            },
            onError: () => {
              usePaymentProcessStore.getState().setCancelDeposit(false)
            }
          }).mutate({ transactionId })
        }
        useUserStore.getState().logout()
        usePortalStore.getState().openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <IdleScreen message='' />
            </Suspense>
          ),
          'idleModal'
        )
      }
    },
    import.meta.env.VITE_IDLE_TIME,
    socket, // Pass the wallet socket instance
    ['USER_WALLET_BALANCE'] // Only listen to this event
  )

  // 4) Listen for “MAINTENANCE_MODE_UPDATE” only when MaintenanceSocketConnection is true:
  const [socketData, setSocketData] = useState(() => {
    const storedData = localStorage.getItem('socketData')
    return storedData ? JSON.parse(storedData) : null
  })

  const ismaintenancemode = socketData?.isActive || false
  if (ismaintenancemode) {
    return <Maintenance initialMinutes={socketData?.remainingMinutes} />
  }

  /**  ******************** USE EFFECTS **************************  **/

  // USE_EFFECT - 1
  useEffect(() => {
    // Check user access
    if (localStorage.getItem('allowedUserAccess') === null || localStorage.getItem('allowedUserAccess') === 'false') {
      deleteCookie('path')
      logOutMutation.mutate()
    }
    // Navigation tracking for /game-play
    const prevPathname = prevPathnameRef.current
    const currentPathname = location.pathname
    if (prevPathname.includes('/game-play/')) {
      const match = prevPathname.match(/\/game-play\/(\d+)/)
      if (match) {
        const gameId = match[1]
        const parameters = {
          gameName: useUserStore.getState().gameName,
          gameId
        }
        if (import.meta.env.VITE_NODE_ENV === 'production') {
          customEvent('game_closed', parameters, userDetail?.userId)
        }
      }
    }
    prevPathnameRef.current = currentPathname
    // Restricted geolocation check
    if (restrictedUser && 'geolocation' in navigator) {
      navigator.permissions.query({ name: 'geolocation' }).then((status) => {
        if (status.state !== 'granted') {
          logOutMutation.mutate()
        }
      })
    }
    // setNavigate once
    setNavigate(navigate)
    // Cleanup on unmount
    document.addEventListener('scroll', listener)
    hideLoader()
    return () => {
      document.removeEventListener('scroll', listener)
    }
  }, [location.pathname, restrictedUser, navigate])

  // USE_EFFECT - 2
  useEffect(() => {
    if (!maintenanceLoading && !maintenanceApiData?.maintenanceModeData?.isActive) {
      localStorage.removeItem('socketData')
    }

    setCookie('onloadGameApi', true)
    // setPragmaticJackpotGc(subCategories?.pragmaticActiveJackpotDetails?.gcType)
    // setPragmaticJackpotSc(subCategories?.pragmaticActiveJackpotDetails?.scType)
    // subCategoryState.setSubCategories(subCategories?.data)
    // subCategoryState.setIsLoading(isLoading)
    // subCategoryState.setRefetch(refetch)
  }, [maintenanceLoading, maintenanceApiData])

  // USE_EFFECT - 3 - SOCKET CONNECTIONS & CLEANUP

  useEffect(() => {
    // If userDetail is null (not logged in), skip entirely

    if (!userDetail) {
      // If userDetail just became null (i.e. logout), disconnect everything and reset the flag
      if (hasConnectedSocketsRef.current) {
        if (socket.connected) {
          socket.disconnect()
          useUserStore.getState().setWalletSocketConnection(false)
        }
        if (tournamentEndSocket.connected) {
          tournamentEndSocket.disconnect()
          useUserStore.getState().setTournamentEndSocketConnection(false)
        }
        if (tournamentLeaderboardSocket.connected) {
          tournamentLeaderboardSocket.disconnect()
          useUserStore.getState().setTournamentLeaderboardSocketConnection(false)
        }
        if (liveWinnerSocket.connected) {
          liveWinnerSocket.disconnect()
          useUserStore.getState().setLiveWinnerSocketConnection(false)
        }
        if (maintenanceSocket.connected) {
          maintenanceSocket.disconnect()
          useUserStore.getState().setMaintenanceSocketConnection(false)
        }
        hasConnectedSocketsRef.current = false
      }
      return
    }

    // If userDetail is non-null (just logged in) AND we haven't connected yet, do it now:
    if (!hasConnectedSocketsRef.current) {
      if (!socket.connected) {
        socket.connect()
        useUserStore.getState().setWalletSocketConnection(true)
      }
      if (!tournamentEndSocket.connected) {
        tournamentEndSocket.connect()
        useUserStore.getState().setTournamentEndSocketConnection(true)
      }
      if (!tournamentLeaderboardSocket.connected) {
        tournamentLeaderboardSocket.connect()
        useUserStore.getState().setTournamentLeaderboardSocketConnection(true)
      }
      if (!liveWinnerSocket.connected) {
        liveWinnerSocket.connect()
        useUserStore.getState().setLiveWinnerSocketConnection(true)
      }
      if (!maintenanceSocket.connected) {
        maintenanceSocket.connect()
        useUserStore.getState().setMaintenanceSocketConnection(true)
      }
      // Mark that we've connected sockets once for this login session:
      hasConnectedSocketsRef.current = true
    }
  }, [userDetail])

  // 2) Listen for “LIMIT_REACHED” only when walletSocketConnection is true:
  useEffect(() => {
    if (!walletSocketConnection) return
    socket.on('LIMIT_REACHED', handleLimitReached)
    socket.on('MULTI_LOGIN', onMultiLogin)
    return () => {
      socket.off('LIMIT_REACHED', handleLimitReached)
      socket.off('MULTI_LOGIN', onMultiLogin)
    }
  }, [walletSocketConnection])

  // USE_EFFECT - 5 - IDLE TIMER BIND EVENTS

  useEffect(() => {
    const shouldActivate = Boolean(userDetail)
    const shouldBindSocket = Boolean(walletSocketConnection)
    if (shouldActivate) {
      idletimer.activate()
    }
    if (shouldBindSocket) {
      idletimer.bindSocketEvents()
    }
    return () => {
      if (shouldBindSocket) {
        idletimer.unbindSocketEvents()
      }
      if (shouldActivate) {
        idletimer.deactivate()
      }
    }
  }, [userDetail, walletSocketConnection])

  // USE_EFFECT - 6 -MAINTENANCE SOCKET EVENT CHECK

  useEffect(() => {
    if (!MaintenanceSocketConnection) return
    const handleMaintenance = (data) => {
      setSocketData(data?.data)
      localStorage.setItem('socketData', JSON.stringify(data?.data || {}))
    }
    maintenanceSocket.on('MAINTENANCE_MODE_UPDATE', handleMaintenance)
    return () => {
      maintenanceSocket.off('MAINTENANCE_MODE_UPDATE', handleMaintenance)
    }
  }, [MaintenanceSocketConnection])

  // USE_EFFECT - 7 - Maintenance Popup handling

  useEffect(() => {
    if (socketData?.isPopup && (!!getLoginToken() || auth?.isAuthenticate)) {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<ModalLoader />}>
            <BasicModal initialMinutes={socketData?.remainingMinutes} />
          </Suspense>
        ),
        'Maintenance Popup'
      )
    }
    if (socketData?.remainingMinutes <= 1) {
      handleLogout()
    }
  }, [socketData])

  // USE_EFFECT - 8 - JACKPOT SOCKET CONNECTION

  useEffect(() => {
    if (jackpotSocket.connected) {
      jackpotSocket.disconnect()
    }
    if (!hasConnectedSocketsRef.current) {
      jackpotSocket.connect()
    }
    if (localStorage.getItem('username') && user.userDetails === null) {
      if (!location.pathname.endsWith('verifyEmail')) {
        getProfileMutation.mutate()
      }
    }
    document.addEventListener('scroll', listener)
    hideLoader()
    return () => {
      document.removeEventListener('scroll', listener)
    }
  }, [])

  // USE_EFFECT - 9 - JACKPOT SOCKET EVENT CHECK

  useEffect(() => {
    if (!jackpotSocket) return
    jackpotSocket.on('JACKPOT_USER_UPDATE', (data) => {
      setJackpotData('jackpotPoolAmount', Number(data?.data?.jackpotPoolAmount))
      setJackpotPoolAmount(Number(data?.data?.jackpotPoolAmount))
      setJackpotData('entryAmount', data?.data?.entryAmount)
      if (data?.data?.newJackpot === true) {
        setNewJackpot(true)
        setTimeout(() => {
          setNewJackpot(false)
        }, 5000)
      }
    })
  }, [jackpotSocket])

  // USE EFFECT - 10 - PRAGMATIC JACKPOT CONNECTION
  useEffect(() => {
    if (pragmaticJackpotSocket.connected) {
      pragmaticJackpotSocket.disconnect()
    }
    if (!hasConnectedSocketsRef.current) {
      pragmaticJackpotSocket.connect()
    }
  }, [])

  // USE_EFFECT - 11 - PRAGMATIC JACKPOT SOCKET EVENT CHECK

  useEffect(() => {
    if (!pragmaticJackpotSocket) return
    pragmaticJackpotSocket.on('PRAGMATIC_JACKPOT_UPDATE', (data) => {
      setPragmaticJackpotGc(data?.data?.gcType)
      setPragmaticJackpotSc(data?.data?.scType)
    })
  }, [pragmaticJackpotSocket])

  return (
    <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID}>
      <Toaster
        position='top-right'
        reverseOrder={false}
        gutter={8}
        toastOptions={{
          duration: 2000,
          style: {
            color: '#000',
            padding: '5px',
            background: '#FDB72E',
            zIndex: '999',
            fontWeight: 'bold'
          }
        }}
      />
      <div className='app-container'>
        <Layout>
          <Suspense fallback={<Loader />}>
            <AppRouter />
          </Suspense>
        </Layout>
      </div>
    </GoogleOAuthProvider>
  )
}

export default App
