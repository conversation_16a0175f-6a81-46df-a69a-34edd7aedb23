import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import { theme } from './theme/index.js'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ThemeProvider } from '@mui/material'
import { BrowserRouter } from 'react-router-dom'
import { ErrorBoundary } from 'react-error-boundary'
import ErrorComponent from './components/ErrorComponent/ErrorComponent.jsx'
import ScrollToTop from './components/ScrollToTop/index.jsx'
import TagManager from "react-gtm-module";
import { HelmetProvider } from 'react-helmet-async'
import CriticalResourcePreloader from './components/CriticalResourcePreloader/index.jsx'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Reduce initial query load
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
})
const root = document.getElementById('root')

const tagManagerArgs = {
  gtmId: "GTM-PR87F2SP",
};

TagManager.initialize(tagManagerArgs);


ReactDOM.createRoot(root).render(
  <QueryClientProvider client={queryClient}>
    <ThemeProvider theme={theme}>
      <BrowserRouter>
      <CriticalResourcePreloader />
      <ScrollToTop />
      <HelmetProvider>
        <ErrorBoundary
          fallbackRender={({ resetErrorBoundary, error }) => {
            return <ErrorComponent resetErrorBoundary={resetErrorBoundary} error={error} />
          }}
          onReset={(details) => {
            console.log('Err', details)
          }}
        >
          <App />
        </ErrorBoundary>
      </HelmetProvider>
      </BrowserRouter>
    </ThemeProvider>
  </QueryClientProvider>
)
