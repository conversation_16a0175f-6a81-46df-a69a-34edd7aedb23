import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, DialogContent, <PERSON>po<PERSON>, <PERSON>rid, Button } from '@mui/material'
import useStyles from './GeocomplyPopup.styles'
import { DiceImg } from '../components/ui-kit/icons/webp'
import { usePortalStore } from '../store/store'
import useIntercom from '../components/SideBar/hooks/useIntercom'
import { toast } from 'react-hot-toast'
import { GeneralQuery } from '../reactQuery'
import { setCookie } from '../utils/cookiesCollection'

const GeocomplyPopup = ({ open, errorData }) => {
  const { showIntercom } = useIntercom(false, true)
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const [vpnProxy, setVPNProxy] = useState(false)
  const [stateNotAllowed, setStateNotAllowed] = useState(false)
  const [sessionKey, setSessionKey] = useState('')
  const [accessData, setAccessData] = useState('')
  // Use local data when testing
  const data = errorData
  useEffect(() => {
    const script = document.createElement('script')
    script.src = 'https://cdn.seonintelligence.com/js/v6/agent.umd.js'
    script.defer = true
    script.onload = () => {
      console.log('SEON script loaded.')
    }
    document.body.appendChild(script)

    return () => {
      document.body.removeChild(script)
    }
  }, [])
  const item = data?.appliedRules?.find((item) => item.operation === 'DECLINE')

  const successAccessToggler = (res) => {
    if (res?.data.status === 200) {
      localStorage.setItem('allowedUserAccess', true)
      toast.success('location verified, please sign in or sign up again')
      goToHome()
    }
  }

  const errorAccessToggler = (error) => {
    console.error('Error accessing the API:', error)
    if (error.response.status === 451) {
      localStorage.setItem('allowedUserAccess', false)
    } else if (
      error?.response?.data?.data?.state === 'DECLINE' ||
      error?.response?.data?.data?.ipDetails?.vpn === true ||
      error?.response?.data?.data?.ipDetails?.web_proxy === true
    ) {
      localStorage.setItem('allowedUserAccess', false)
      setAccessData(error?.response?.data?.data)
    }
  }
 

  const initSeon = async () => {
    if (typeof seon === 'undefined') {
      console.error('SEON script not loaded yet.')
      return
    }
    else {
      seon.init()
      const config = {
        geolocation: {
          canPrompt: true,
          enabled: true,
          maxAgeSeconds: 0
        },
        networkTimeoutMs: 2000,
        fieldTimeoutMs: 2000,
        region: 'eu',
        silentMode: true
      }
      try {
        const session = await seon.getSession(config)
        setSessionKey(session)
      } catch (error) {
        console.error('Error initializing Seon session:', error)
      }
    }
  }
  // const initSeon = async () => {
  //   seon.init()
  //   const config = {
  //     geolocation: {
  //       canPrompt: true,
  //       enabled: true,
  //       maxAgeSeconds: 0
  //     },
  //     networkTimeoutMs: 2000,
  //     fieldTimeoutMs: 2000,
  //     region: 'eu',
  //     silentMode: true
  //   }
  //   try {
  //     const session = await seon.getSession(config) // eslint-disable-line
  //     setSessionKey(session)
  //   } catch (error) {
  //     console.error('Error initializing Seon session:', error)
  //   }
  // }
  useEffect(() => {
    if (sessionKey !== '') {
      accessMutate.mutate()
    }
  }, [sessionKey])
  const accessMutate = GeneralQuery.getAccessQuery({
    params: { sessionKey: sessionKey, rtyuioo: sessionKey === ' ' },
    successAccessToggler,
    errorAccessToggler
  })

  useEffect(() => {
    const accessItem = accessData?.appliedRules?.find((item) => item.operation === 'DECLINE')
    if (
      data?.ipDetails?.vpn ||
      data?.ipDetails?.web_proxy ||
      accessData?.ipDetails?.vpn ||
      accessData?.ipDetails?.web_proxy
    ) {
      setVPNProxy(true)
    } else if (data?.ipDetails?.country !== 'US' || accessData?.ipDetails?.country !== 'US') {
      setCookie('restrictedZoneUser', true)
      setStateNotAllowed(true)
    } else if (
      item?.name === 'IP/STATE Not allowed - Individual' ||
      accessItem?.name === 'IP/STATE Not allowed -Individual'
    ) {
      setCookie('restrictedZoneUser', true)
      setStateNotAllowed(true)
    }
  }, [data, accessData])

  const handleContact = () => {
    showIntercom()
  }

  const goToHome = () => {
    portalStore.closePortal()
    window.location.reload()
  }

  const handleGPS = () => {
    if ('geolocation' in navigator) {
      navigator.permissions.query({ name: 'geolocation' }).then((permissionStatus) => {
        if (permissionStatus.state === 'granted') {
          initSeon()
          toast.error('Your GPS is already enabled.')
          return
        }

        navigator.geolocation.getCurrentPosition(
          (position) => {
            initSeon()
          },
          (error) => {
            toast.error('Location detection failed. Ensure location services are on and try again.')
            console.error('Error obtaining location:', error)
          }
        )
      })
    } else {
      console.log('Geolocation API is not supported in this browser.')
    }
  }

  return (
    <Dialog open={open} className={classes.geoModal}>
      <DialogContent>
        <Grid className='modal-header'>
          <Grid className='geo-logo-wrap'>
            <img src={DiceImg} alt='Casino' />
          </Grid>
        </Grid>
        <Grid className='geo-modal-content'>
          <Typography variant='h4'>Access Restricted</Typography>
          {vpnProxy || stateNotAllowed ? (
            <Typography>
              The Money Factory is currently only available in the United States (Excluding States: NY, ID, LA, MI, MT, NV,
              WA)
            </Typography>
          ) : (
            <Typography>Your access has been blocked due to security concerns.</Typography>
          )}

          <Grid className='region-wrap'>
            {vpnProxy || stateNotAllowed ? (
              <>
                <Typography variant='h5' style={{ paddingBottom: '0' }}>
                  Your region: {data?.ipDetails?.state_prov}
                </Typography>
                <Typography variant='h6'>Your IP: {data?.ipDetails?.ip} </Typography>
              </>
            ) : null}
          </Grid>

          {vpnProxy && (
            <Grid className='vpn-card-wrap'>
              <Grid className='vpn-card'>VPN/Proxy Detected</Grid>
            </Grid>
          )}

          {!stateNotAllowed && !vpnProxy && (
            <Typography>
              If you believe this was a mistake, first{' '}
              <a href='#' onClick={handleGPS}>
                ENABLE GPS
              </a>{' '}
              to verify your location. If the issue persists, please{' '}
              <a
                href='#'
                onClick={(e) => {
                  e.preventDefault()
                  handleContact()
                }}
              >
                Contact Support
              </a>{' '}
              for further assistance.
            </Typography>
          )}

          {stateNotAllowed && !vpnProxy && (
            <Typography>
              If the location above is incorrect, please{' '}
              <a href='#' onClick={handleGPS}>
                enable GPS
              </a>{' '}
              to continue playing or{' '}
              <a
                href='#'
                onClick={(e) => {
                  e.preventDefault()
                  handleContact()
                }}
              >
                Contact Support
              </a>{' '}
              for any additional questions.
            </Typography>
          )}

          {vpnProxy && (
            <Typography>
              Please{' '}
              <a
                href='#'
                onClick={(e) => {
                  e.preventDefault()
                  handleContact()
                }}
              >
                Contact Support
              </a>{' '}
              for any additional questions.
            </Typography>
          )}

          {!vpnProxy && (
            <Button
              className='btn btn-primary'
              style={{ marginBottom: '0.875rem' }}
              variant='contained'
              onClick={handleGPS}
            >
              Enable GPS
            </Button>
          )}

          <Typography>
            Kind Regards, <br />
            The Money Factory
          </Typography>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default GeocomplyPopup
