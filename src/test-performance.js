/**
 * Simple performance test to verify optimizations
 * Run this in browser console to test route performance
 */

// Test 1: Check if components are lazy loaded
console.log('🧪 Testing Route Performance Optimizations...\n')

// Test 2: Measure route navigation performance
const testRouteNavigation = () => {
  const startTime = performance.now()
  
  // Simulate route change
  window.history.pushState({}, '', '/games')
  window.dispatchEvent(new PopStateEvent('popstate'))
  
  setTimeout(() => {
    const endTime = performance.now()
    console.log(`📊 Route navigation took: ${(endTime - startTime).toFixed(2)}ms`)
    
    if (endTime - startTime < 100) {
      console.log('✅ Excellent performance (<100ms)')
    } else if (endTime - startTime < 300) {
      console.log('✅ Good performance (<300ms)')
    } else {
      console.log('⚠️ Could be optimized (>300ms)')
    }
  }, 100)
}

// Test 3: Check bundle splitting
const checkBundleSplitting = () => {
  const scripts = Array.from(document.querySelectorAll('script[src]'))
  const chunks = scripts.filter(script => script.src.includes('chunk') || script.src.includes('lazy'))
  
  console.log(`📦 Found ${chunks.length} code-split chunks`)
  console.log('📦 Bundle splitting:', chunks.length > 0 ? '✅ Active' : '❌ Not detected')
}

// Test 4: Check for skeleton loaders
const checkSkeletonLoaders = () => {
  const hasSkeletons = document.querySelector('[role="status"]') !== null
  console.log('💀 Skeleton loaders:', hasSkeletons ? '✅ Implemented' : '❌ Not found')
}

// Test 5: Memory usage check
const checkMemoryUsage = () => {
  if (performance.memory) {
    const memory = performance.memory
    console.log(`🧠 Memory usage:`)
    console.log(`   Used: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
    console.log(`   Total: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
    console.log(`   Limit: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`)
  }
}

// Run all tests
const runPerformanceTests = () => {
  console.clear()
  console.log('🚀 Route Performance Test Suite\n')
  
  checkBundleSplitting()
  checkSkeletonLoaders()
  checkMemoryUsage()
  
  console.log('\n📈 Testing route navigation...')
  testRouteNavigation()
  
  console.log('\n✨ Performance test completed!')
  console.log('💡 Tip: Open Network tab to see lazy loading in action')
}

// Export for use in console
window.runPerformanceTests = runPerformanceTests

// Auto-run if in development
if (process.env.NODE_ENV === 'development') {
  setTimeout(runPerformanceTests, 2000)
}

export default runPerformanceTests
