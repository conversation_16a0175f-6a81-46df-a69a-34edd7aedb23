import * as Yup from 'yup'

const validatorMap = {
  text: (field) => {
    let schema = Yup.string()
    if (field?.required) schema = schema.required('This field is required')
    schema = schema.test('no-spaces', '<PERSON>ela<PERSON> enter a valid input', (value) => {
      if (!value) return !field?.required
      return value.trim().length > 0 // Trim and check if the value has length
    }).matches(
      /^[a-zA-Z0-9 ]*$/,
      'Special characters are not allowed'
    )
    return schema
  },
  email: (field) => {
    let schema = Yup.string()
    if (field?.required) {
      schema = schema.required('This field is required')
    }
    schema = schema
      .max(150, 'Email must be at most 150 characters')
      .test('no-leading-trailing-spaces', 'Email should not have leading or trailing spaces', (value) => {
        if (!value) return !field?.required
        return value.trim() === value
      })
      .test('valid-email-format', 'Invalid email address', (value) => {
        if (!value) return !field?.required
        return /^(([^<>()[\]\\.,+;:\s@"]+(\.[^<>()[\]\\.,+;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
          value
        )
      })
    return schema
  },

  number: (field) => {
    let schema = Yup.string()
    if (field?.required) schema = schema.required('This field is required')
    if (field?.moreDetails?.min) {
      schema = schema.test('min', `Number should not be less than ${field?.moreDetails?.min}`, (value) => {
        if (!value) return !field?.required
        return Number(value) >= +field.moreDetails?.min
      })
    }
    if (field?.moreDetails?.max) {
      schema = schema.test('max', `Number should not be more than ${field?.moreDetails?.max}`, (value) => {
        if (!value) return !field?.required
        return Number(value) <= +field?.moreDetails?.max
      })
    }
    return schema
  },

  checkbox: (field) => {
    let schema = Yup.array().of(Yup.string()).default([])
    if (field?.required) schema = schema.min(1, 'Select Atleast one value')
    return schema
  },

  select: (field) => {
    let schema = Yup.string()
    if (field?.required) schema = schema.required('Please select an option.')
    return schema
  },
  textarea: (field) => {
    let schema = Yup.string()
    if (field?.required) schema = schema.required('This field is required')
    schema = schema.test('no-spaces', 'Pelase enter a valid input', (value) => {
      if (!value) return !field?.required
      return value.trim().length > 0 // Trim and check if the value has length
    })
    return schema
  },
  radio: (field) => {
    let schema = Yup.string()
    if (field?.required) schema = schema.required('Please select an option.')
    return schema
  }
}

const buildValidationSchema = (fields, extraValidation = {}) => {
  const shape = {}
  fields?.length > 0 &&
    fields.forEach((field) => {
      const builder = validatorMap[field?.frontendQuestionType] || (() => Yup.mixed())
      let schema = builder(field)
      if (extraValidation[field?.id]) {
        schema = extraValidation[field.id](schema)
      }
      shape[field.questionnaireId] = schema
    })

  return Yup.object().shape(shape)
}

export default buildValidationSchema
