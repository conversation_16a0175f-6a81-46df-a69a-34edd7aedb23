import React, { useEffect } from 'react'
import snsWebSdk from '@sumsub/websdk'
import { AccVerifyQuery, useGetProfileMutation } from '../../../../reactQuery'
import { useUserStore } from '../../../../store/useUserSlice'
import { usePortalStore } from '../../../../store/userPortalSlice'
import useStepperStore from '../../../../store/useStepperStore'
import TagManager from 'react-gtm-module'

const KYCSection = ({ handleClose, setInitializeKYC }) => {
  const { stepperCalledFor } = useStepperStore()
  const portalStore = usePortalStore()
  const user = useUserStore((state) => state)
  const successHandler = (data) => {}

  const errorHandler = () => {
    setInitializeKYC(false)
  }

  const { data, isLoading } = AccVerifyQuery.initKYCQuery({ errorHandler, successHandler })

  useEffect(() => {
    if (!isLoading && data) {
      launchWebSdk(data?.token, user?.userDetails?.email, user?.userDetails?.phone)
    }
  }, [data, isLoading])

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails({ ...res?.data?.data })
      setTimeout(() => {
        handleClose()
        if (stepperCalledFor === 'kycSettings') portalStore.closePortal()
      }, 5000)
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  /**
   * @param accessToken - access token that you generated on the backend in Step 2
   * @param applicantEmail - applicant email (not required)
   * @param applicantPhone - applicant phone, if available (not required)
   * @param customI18nMessages - customized locale messages for current session (not required)
   */
  function launchWebSdk(accessToken, applicantEmail, applicantPhone, customI18nMessages) {
    let snsWebSdkInstance = snsWebSdk
      .init(
        accessToken,
        // token update callback, must return Promise
        // Access token expired
        // get a new one and pass it to the callback to re-initiate the WebSDK
        () => this.getNewAccessToken()
      )
      .withConf({
        lang: 'en', //language of WebSDK texts and comments (ISO 639-1 format)
        email: applicantEmail,
        phone: applicantPhone,
        theme: 'dark'
      })
      .withOptions({ addViewportTag: false, adaptIframeHeight: true })
      // see below what kind of messages WebSDK generates
      .on('idCheck.onStepCompleted', (payload) => {
        TagManager.dataLayer({
          dataLayer: {
            event: 'KYC Verification',
            event_step_completed: payload.idDocSetType,
            user_id: user?.userDetails?.userId,
            email: applicantEmail,
            phone: applicantPhone
          }
        })
      })
      .on('idCheck.onApplicantStatusChanged', (payload) => {
        if (payload.reviewStatus === 'completed') {
          TagManager.dataLayer({
            dataLayer: {
              event: 'KYC Verification',
              event_status: payload.reviewStatus,
              user_id: user?.userDetails?.userId,
              email: applicantEmail,
              phone: applicantPhone
            }
          })
          // setTimeout(() => {
          getProfileMutation.mutate()
          // }, 2000)
        }
      })
      .on('idCheck.onError', (error) => {
        console.log('onError kyc', error)
        // TagManager.dataLayer({
        //  dataLayer:{ event: 'KYC Verification error',
        //   error_text: error?.error,
        //   user_id: user?.userDetails?.userId,
        //   email: user?.userDetails?.email,
        //   phone: user?.userDetails?.phone
        //  }
        // })
      })
      .build()

    // you are ready to go:
    // just launch the WebSDK by providing the container element for it
    snsWebSdkInstance.launch('#sumsub-websdk-container')
  }

  return (
    <div>
      <div id='sumsub-websdk-container' />
    </div>
  )
}

export default KYCSection
