import React, { useState } from 'react'
import useStyles from './ReferPage.styles'
import {
  Button,
  Grid,
  Typography,
  Box,
  Link,
  Accordion,
  AccordionSummary,
  Table,
  TableHead,
  TableRow,
  TableCell,
  AccordionDetails,
  TableBody,
  TableContainer,
  Tooltip,
  CircularProgress,
  Pagination
} from '@mui/material'
import ScrollToTop from '../../components/ScrollToTop'
import ContentCopySharpIcon from '@mui/icons-material/ContentCopySharp'
import FriendsIcon from '../../components/ui-kit/icons/svg/friends.svg'
import PaymentCard from '../../components/ui-kit/icons/svg/card.svg'
import ReffFacebook from '../../components/ui-kit/icons/svg/reff-facebook.svg'
import ReffInstagram from '../../components/ui-kit/icons/svg/reff-instagram.svg'
import ReffSms from '../../components/ui-kit/icons/svg/reff-sms.svg'
import ReffTelegram from '../../components/ui-kit/icons/svg/reff-telegram.svg'
import ReffTwitter from '../../components/ui-kit/icons/svg/reff-twitter.svg'
import ReffWhtsapp from '../../components/ui-kit/icons/svg/reff-whtsapp.svg'

import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import Paper from '@mui/material/Paper'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import referDetailsQuery from '../../reactQuery/referDetailsQuery'
import referAFriendQuery from '../../reactQuery/ReferAFriend'
import toast from 'react-hot-toast'
import { copyToClipBoard } from '../../utils/helpers'
import { useUserStore } from '../../store/useUserSlice'
import { useClaimReferralBonusMutation } from '../../reactQuery/bonusQuery'
import BannerManagement from '../../components/BannerManagement'
import { useBannerStore } from '../../store/useBannerSlice'
/* eslint-disable multiline-ternary */

const ReferPage = () => {
  const classes = useStyles()
  const [isloading, setIsLoading] = useState(false)
  const [limit, setLimit] = useState(15)
  const [pageNo, setPageNo] = useState(1)

  const { referAfriend } = useBannerStore((state) => state)
  const handleChange = (event, value) => {
    setPageNo(value)
  }
  const mutationClaimReferralBonus = useClaimReferralBonusMutation({
    onSuccess: (response) => {
      if (response?.data?.success) {
        toast.success('Referral bonus claimed successfully!')
        setIsLoading(false)
        refetchReferFriend()
        refetchReferDetails()
      }
    },
    onError: (error) => {
      console.log(error)
      // toast.error('Failed to claim referral bonus. Please try again.');
    }
  })
  const handleClaimButton = (userId) => {
    setIsLoading(true)
    mutationClaimReferralBonus.mutate({ referredUserId: userId })
  }

  const onSuccess = (data) => {
    // Handle successful data retrieval
    // console.log('Data retrieved successfully:', data)
  }

  const onError = (error) => {
    // Handle error
    console.error('Error fetching data:', error)
  }

  const { data, refetch: refetchReferFriend } = referAFriendQuery({ onSuccess, onError })
  const {
    data: referDetails,
    refetch: refetchReferDetails,
    loading: isReferDetailsLoading
  } = referDetailsQuery({ params: { limit, page: pageNo } })

  const referalBonus = data?.referralBonus

  const userDetails = useUserStore((state) => state.userDetails)

  const copyToClipboard = () => {
    navigator.clipboard.writeText(data?.referralLink).then(() => {
      toast.success('Link copied to clipboard! Now you can paste it on Instagram.')
    })
  }

  const handleCopyCode = (e) => {
    const isCopySuccessfull = copyToClipBoard(`${data.referralLink}`)
    if (!isCopySuccessfull) {
      return toast.error('Failed to copy code!')
    }
    toast.success('Referral link copied!')
    return null
  }

  return (
    <>
      <ScrollToTop />
      <Grid className={classes.lobbyRight}>
        <Box className='reffer-friend-page'>
          <Grid>
            <BannerManagement bannerData={referAfriend} />
          </Grid>
          <Box className='shewwp-coins-section'>
            <Typography variant='h4'>Get Up to {data?.maxSc.toFixed(2)} Sweepstakes Coins Free! </Typography>
            <Typography>
              Get up to <span className='gc-coin-text'>{data?.maxGc.toFixed(2)} Gold Coins</span> and{' '}
              <span className='sc-coin-text'>{data?.maxSc.toFixed(2)} Sweepstakes Coins</span> free when you refer a
              friend to play at The Money Factory.
            </Typography>
          </Box>
          <Box className='share-reffer-section'>
            <Grid className='inner-heading'>
              <Typography variant='h4'>Share your referral link with friends</Typography>
            </Grid>

            <Box className='reffer-theme-card'>
              <Grid className='share-reffer-left'>
                <Grid className='share-reffer-heading'>
                  <Typography variant='h4'>Share your link.</Typography>
                  <Typography variant='h4' className='green-text'>
                    More friends = More rewards.
                  </Typography>
                </Grid>
                <Grid className='social-listing-wrap'>
                  <Grid className='social-listing'>
                    <Link href='javascripr:void(0);'>
                      <a
                        href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                          `Get exclusive perks on The Money Factory by signing up with my referral link: ${data?.referralLink}`
                        )}`}
                        target='_blank'
                        rel='noopener noreferrer'
                      >
                        <img src={ReffFacebook} alt='Facebook' />
                      </a>
                    </Link>
                    <Link href='javascripr:void(0);'>
                      <Button onClick={copyToClipboard}>
                        <img src={ReffInstagram} alt='Instagram' />
                      </Button>
                    </Link>
                    <Link href='javascripr:void(0);'>
                      <a
                        href={`https://api.whatsapp.com/send?text=${encodeURIComponent(
                          `Get exclusive perks on The Money Factory by signing up with my referral link: ${data?.referralLink}`
                        )}`}
                        target='_blank'
                        rel='noopener noreferrer'
                      >
                        <img src={ReffWhtsapp} alt='WhatsApp' />
                      </a>
                    </Link>
                    <Link href='javascripr:void(0);'>
                      <a
                        href={`https://telegram.me/share/url?url=${encodeURIComponent(
                          `Get exclusive perks on The Money Factory by signing up with my referral link: ${data?.referralLink}`
                        )}`}
                        target='_blank'
                        rel='noopener noreferrer'
                      >
                        <img src={ReffTelegram} alt='Telegram' />
                      </a>
                    </Link>
                    {userDetails?.deviceType === 'mobile' ? (
                      <Link href='javascripr:void(0);'>
                        <a
                          href={`sms:?body=${encodeURIComponent(
                            `Get exclusive perks on The Money Factory by signing up with my referral link: ${data?.referralLink}`
                          )}`}
                          target='_blank'
                          rel='noopener noreferrer'
                        >
                          <img src={ReffSms} alt='SMS' />
                        </a>
                      </Link>
                    ) : (
                      <></>
                    )}
                    <Link href='javascripr:void(0);'>
                      <a
                        href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(
                          `Get exclusive perks on The Money Factory by signing up with my referral link: ${data?.referralLink}`
                        )}`}
                        target='_blank'
                        rel='noopener noreferrer'
                      >
                        <img src={ReffTwitter} alt='Twitter' />
                      </a>
                    </Link>
                  </Grid>

                  <Button type='button' className='btn btn-primary' onClick={handleCopyCode}>
                    <ContentCopySharpIcon />
                    Copy Link
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Box>
          <Box className='how-section'>
            <Grid className='inner-heading'>
              <Typography variant='h4'>How does it work?</Typography>
            </Grid>
            <Box className='reffer-theme-card'>
              <Grid className='how-it-works-listing'>
                <ol>
                  <li>
                    <Grid className='how-it-works-list-card'>Share your unique referral link with your friends.</Grid>
                  </li>
                  <ul>
                    {referalBonus?.length > 0 && (
                      <li key={referalBonus[0]?.bonusId}>
                        <Grid className='how-it-works-list-card'>
                          Earn <span className='gc-coin-text'>{referalBonus[0]?.gcAmount} Gold Coins</span> and{' '}
                          <span className='sc-coin-text'>{referalBonus[0]?.scAmount} Sweepstakes Coins</span> when your
                          friend makes their first purchase totaling ${referalBonus[0]?.minimumPurchase} with a verified
                          account.
                        </Grid>
                      </li>
                    )}

                    {referalBonus?.slice(1)?.map((bonus, index) => {
                      // totalMinPurchase += bonus.minimumPurchase

                      return (
                        <li key={bonus?.bonusId}>
                          <Grid className='how-it-works-list-card'>
                            Earn an additional <span className='gc-coin-text'>{bonus?.gcAmount} Gold Coins</span> and{' '}
                            <span className='sc-coin-text'>{bonus?.scAmount} Sweepstakes Coins</span> when your friend
                            makes additional purchases totaling ${bonus?.minimumPurchase} or more.
                          </Grid>
                        </li>
                      )
                    })}
                  </ul>
                </ol>
              </Grid>
            </Box>
          </Box>
          <Box className='friend-section'>
            <Grid className='inner-heading'>
              <Typography variant='h4'>Friends Statistics</Typography>
            </Grid>
            <Grid className='statistics-table-wrap'>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />} aria-controls='panel1-content' id='panel1-header'>
                  <Grid className='accordian-grid'>
                    <Grid className='accordian-card'>
                      <img src={FriendsIcon} alt='Friends' />
                      <Grid className='accordian-card-right'>
                        <Typography variant='h4'>{data?.referredUsers}</Typography>
                        <Typography>Referred Friends</Typography>
                      </Grid>
                    </Grid>
                    <Grid className='accordian-card'>
                      <img src={PaymentCard} alt='Purchases' />
                      <Grid className='accordian-card-right'>
                        <Typography variant='h4'>{data?.qualifiedUsers}</Typography>
                        <Typography>Friends Purchased</Typography>
                      </Grid>
                    </Grid>
                    <Grid className='accordian-card'>
                      <img src={usdchipIcon} alt='Friends' />
                      <Grid className='accordian-card-right'>
                        <Typography variant='h4' className='gc-coin-text'>
                          {data?.gcCoinsEarned}
                        </Typography>
                        <Typography>Gold Coins Earned</Typography>
                      </Grid>
                    </Grid>
                    <Grid className='accordian-card last-card'>
                      <img src={usdIcon} alt='Friends' />
                      <Grid className='accordian-card-right'>
                        <Typography variant='h4' className='sc-coin-text'>
                          {data?.scCoinsEarned}
                        </Typography>
                        <Typography>Sweep Coins Earned</Typography>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid className='mob-accordian'>
                    <Grid className='accordian-card'>
                      <Grid className='accordian-card-content'>
                        <img src={FriendsIcon} alt='Friends' />
                        <Typography variant='h4'>{data?.referredUsers}</Typography>
                      </Grid>
                      <Grid className='accordian-card-right'>
                        <Typography>Referred Friends</Typography>
                      </Grid>
                    </Grid>
                    <Grid className='accordian-card'>
                      <Grid className='accordian-card-content'>
                        <img src={PaymentCard} alt='Purchases' />
                        <Typography variant='h4'>{data?.qualifiedUsers}</Typography>
                      </Grid>
                      <Grid className='accordian-card-right'>
                        <Typography>Friends Purchases</Typography>
                      </Grid>
                    </Grid>
                    <Grid className='accordian-card'>
                      <Grid className='accordian-card-content'>
                        <img src={usdchipIcon} alt='Friends' />
                        <Typography variant='h4' className='gc-coin-text'>
                          {data?.gcCoinsEarned?.toFixed(2)}
                        </Typography>
                      </Grid>
                      <Grid className='accordian-card-right'>
                        <Typography>Gold Coins earned</Typography>
                      </Grid>
                    </Grid>
                    <Grid className='accordian-card'>
                      <Grid className='accordian-card-content'>
                        <img src={usdIcon} alt='Friends' />
                        <Typography variant='h4' className='sc-coin-text'>
                          {data?.scCoinsEarned?.toFixed(2)}
                        </Typography>
                      </Grid>
                      <Grid className='accordian-card-right'>
                        <Typography>Sweep Coins earned</Typography>
                      </Grid>
                    </Grid>
                  </Grid>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper}>
                    <Table sx={{ minWidth: 650 }} aria-label='simple table'>
                      <TableHead>
                        <TableRow>
                          <TableCell align='left'>Email</TableCell>
                          <TableCell align='left'>User</TableCell>
                          {/* <TableCell align='left'>Time</TableCell> */}
                          <TableCell align='left'>Purchase Amount</TableCell>
                          <TableCell align='left'>Earned coin</TableCell>
                          <TableCell align='left'> Pending To Claim</TableCell>
                          <TableCell align='center'> Claim</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {referDetails?.referralDetails?.map((referral) => (
                          <TableRow key={referral?.userId}>
                            <TableCell component='td' scope='row'>
                              {referral?.email}
                            </TableCell>
                            <TableCell align='right'>
                              <Grid className='user-details'>
                                {/* <img src={TableUser} alt='User' /> */}
                                {referral?.username}
                              </Grid>
                            </TableCell>
                            {/* <TableCell align='right'>
                              {new Date(referral?.createdAt).toLocaleTimeString()}
                            </TableCell> */}
                            <TableCell align='right'>
                              <Grid className='table-dllor'>
                                {/* <img src={Dollor} alt='Dollor' /> */}
                                {referral?.totalPurchaseAmount.toFixed(2)}
                              </Grid>
                            </TableCell>
                            <TableCell align='right'>
                              <Grid className='table-dllor'>
                                {/* <img src={Dollor} alt='Dollor' /> */}
                                <span className='gc-coin-text'>{referral?.gcEarn?.toFixed(2)} GC </span>
                                <span>+</span>
                                <span className='sc-coin-text'>{referral?.scEarn?.toFixed(2)} SC</span>
                              </Grid>
                            </TableCell>
                            <TableCell align='right'>
                              <Grid className='table-dllor'>
                                {/* <img src={Dollor} alt='Dollor' /> */}
                                <span className='gc-coin-text'>{referral?.pendingClaimGc?.toFixed(2)} GC </span>
                                <span>+</span>
                                <span className='sc-coin-text'>{referral?.pendingClaimSc?.toFixed(2)} SC</span>
                              </Grid>
                            </TableCell>
                            <TableCell align='center'>
                              {referral?.isClaimEnable === false ? (
                                <Tooltip
                                  enterTouchDelay={0}
                                  title={
                                    referral?.kycStatus !== 'K4' && referral?.kycStatus !== 'K5' ? (
                                      <span style={{ fontSize: '1rem', color: 'yellow' }}>
                                        To claim this, your friend must complete KYC verification
                                      </span>
                                    ) : (
                                      ''
                                    )
                                  }
                                >
                                  <span>
                                    <Button variant='contained' className='btn btn-primary' style={{ opacity: '0.5' }}>
                                      Claim
                                    </Button>
                                  </span>
                                </Tooltip>
                              ) : (
                                <Button
                                  variant='contained'
                                  className='btn btn-primary'
                                  onClick={() => {
                                    handleClaimButton(referral?.userId)
                                  }}
                                  disabled={
                                    referral?.kycStatus === 'K1' ||
                                    referral?.kycStatus === 'K2' ||
                                    referral?.kycStatus === 'K3' ||
                                    referral?.kycStatus === 'K0' ||
                                    isloading
                                  }
                                >
                                  Claim{isloading && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    {referDetails?.count > 0 && !isReferDetailsLoading && (
                      <Pagination
                        className={classes.tablePagination}
                        count={Math.ceil(referDetails?.count / limit)}
                        page={pageNo}
                        onChange={handleChange}
                        defaultPage={3}
                        siblingCount={1}
                      />
                    )}
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Box>

          <Grid>
            <Typography>
              {' '}
              Disclaimer : The refer-a-friend program was updated on September 4, 2024. The updated bonus coins system
              is only available to newly referred players who have not made any previous purchases. If a referred
              account was created before September 4, 2024, and has already made a purchase, it will not qualify for any
              additional bonuses under the new program.
            </Typography>
          </Grid>
        </Box>
      </Grid>
    </>
  )
}

export default ReferPage
