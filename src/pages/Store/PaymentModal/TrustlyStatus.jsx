import { useEffect, lazy, Suspense, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { paymentSocket } from '../../../utils/socket'
import { usePortalStore, useUserStore } from '../../../store/store'
import PaymentStatus from '../../../components/PaymentStatus'
import toast from 'react-hot-toast'
import RetryPaymentDialog from './RetryPaymentDialog'
import { initiateTrustlyScript } from './utils/trustly/initiators'

import PaymentConnectionPopup from './components/PaymentConnectionPopup'
import PaymentLoader from '../../../components/Loader/PaymentLoader'
import { useGetProfileMutation } from '../../../reactQuery'
import ScratchCardComponent from '../../../components/ScratchCard/ScratchCardComponent'
import { useQuery } from '@tanstack/react-query'
import { getFreeSpin } from '../../../utils/apiCalls'
import ModalLoader from '../../../components/ui-kit/ModalLoader'
const FreeSpinModal = lazy(() => import('../../../components/FreeSpinModal/FreeSpinModal'));

const TrustlyStatus = ({ onAccountRefresh = () => { }, isRenderInPaymentStatus, setShowTrustlyLoader = () => { } }) => {
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const [userBonusId, setUserBonusId] = useState(null)
  const { data: freeSpindata, isLoading: freeLoading } = useQuery({
    queryKey: ['freespin', userBonusId],

    queryFn: ({ queryKey }) => {
      return getFreeSpin({ userBonusId: queryKey[1] });
    },
    select: (res) => res?.data?.freeSpinBonus,
    enabled: !!userBonusId,
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      if (res) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader/>}>
              <FreeSpinModal data={res?.data?.freeSpinBonus} />
            </Suspense>
          ),
          'freeSpinModal'
        )
      }
    }
  })
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      const userData = res?.data?.data
      if (userData) {
        setUserDetails(userData)
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  useEffect(() => {
    let isMounted = true
    let isConnected = false

    const timeout = setTimeout(() => {
      if (!isConnected && isMounted) {
        console.error('No Trustly event or connection within 15 seconds.')
        setShowTrustlyLoader(false)
        portalStore.openPortal(() => <PaymentConnectionPopup navigate={navigate} />, 'trustlyConnection')
      }
    }, 15000)

    const markTrigger = () => {
      isConnected = true
      clearTimeout(timeout)
    }

    const onConnect = () => {
      console.log('#### Payment Socket Connected:', paymentSocket.id)
    }

    const onSuccess = (payment = {}) => {
      markTrigger()
      const data = {
        transactionId: payment?.data?.transactionId,
        status: payment?.data?.status?.toLowerCase(),
        paymentMethod: payment?.data?.paymentMethod,
        scCoin: payment?.data?.scCoin,
        gcCoin: payment?.data?.gcCoin,
        bonusSc: payment?.data?.bonusSc,
        bonusGc: payment?.data?.bonusGc,
        amount: payment?.data?.amount,
        scratchCardBonus: payment?.data?.scratchCardBonus,
        userBonusId: payment?.data?.userBonusId,
        rewardType:payment?.data?.rewardType,
        freeSpinUserBonusId: payment?.data?.freeSpinUserBonusId
      }
      if (payment?.data?.scratchCardBonus) {
        setTimeout(() => {
          portalStore.openPortal(() => <ScratchCardComponent
            scratchCardBonus={payment?.data?.scratchCardBonus}
            userBonusId={payment?.data?.userBonusId}
            rewardType={payment?.data?.rewardType}
            parentMessage={payment?.data?.parentMessage}
            childMessage={payment?.data?.childMessage}
          />, 'bonusStreak')
        }, 9000);
      }
      else if (payment?.data?.freeSpinUserBonusId) {
        setUserBonusId(payment?.data?.freeSpinUserBonusId)
      }
      getProfileMutation.mutate()
      if (isRenderInPaymentStatus) {
        navigate('/user/store')
        portalStore.openPortal(() => (
          <PaymentStatus paymentDetails={data} isTrustlyStatusModal isTrustly />
        ), 'loginModal')
      } else {
        portalStore.openPortal(() => (
          <PaymentStatus paymentDetails={data} isTrustly />
        ), 'loginModal')
      }
    }

    const onFailed = (payment = {}) => {
      markTrigger()
      const data = {
        transactionId: payment?.data?.transactionId,
        status: payment?.data?.status?.toLowerCase(),
        paymentMethod: payment?.data?.paymentMethod,
        scCoin: payment?.data?.scCoin,
        gcCoin: payment?.data?.gcCoin,
        bonusSc: payment?.data?.bonusSc,
        bonusGc: payment?.data?.bonusGc,
        amount: payment?.data?.amount,
        message: payment?.data?.message
      }

      if (isRenderInPaymentStatus) {
        navigate('/user/store')
        portalStore.openPortal(() => (
          <PaymentStatus paymentDetails={data} isTrustlyStatusModal isCancelTrustlyWorkflow isTrustly />
        ), 'loginModal')
      } else {
        portalStore.openPortal(() => (
          <PaymentStatus paymentDetails={data} isTrustly />
        ), 'loginModal')
      }
    }

    const onRefresh = (payment = {}) => {
      markTrigger()
      navigate('/user/store')
      setShowTrustlyLoader(true)
      toast(payment?.data?.message)
      const establishData = payment?.data?.establishData
      const trustlyOptions = payment?.data?.TrustlyOptions
      onAccountRefresh(establishData, trustlyOptions)
    }

    const onRetry = (payment = {}) => {
      markTrigger()
      const establishData = payment?.data?.establishData
      const trustlyOptions = payment?.data?.TrustlyOptions
      const emitDepositObj = payment?.data?.emitDepositObj
      const transactionFailed = payment?.data?.transactionFailed

      setShowTrustlyLoader(false)

      const updatedEmitDeposit = {
        ...emitDepositObj,
        status: emitDepositObj?.status?.toLowerCase()
      }

      if (transactionFailed) {
        navigate('/user/store')
        portalStore.openPortal(() => (
          <RetryPaymentDialog
            emitDepositObj={emitDepositObj}
            onRetry={() => {
              initiateTrustlyScript({
                callback: () => {
                  Trustly.selectBankWidget(establishData, trustlyOptions)
                },
                failure: () => {
                  console.log('Trustly Script Failure')
                  navigate('/user/store')
                }
              })
            }}
            onCancel={() => {
              portalStore.openPortal(() => (
                <PaymentStatus
                  paymentDetails={updatedEmitDeposit}
                  isTrustlyStatusModal
                  isCancelTrustlyWorkflow
                  isTrustly
                />
              ), 'loginModal')
            }}
          />
        ), 'retryCancelPopup')
      }
    }

    const setupListeners = () => {
      paymentSocket.on('connect', onConnect)
      paymentSocket.on('PAYMENT_DEPOSIT_SUCCESS', onSuccess)
      paymentSocket.on('PAYMENT_DEPOSIT_FAILED', onFailed)
      paymentSocket.on('PAYMENT_DEPOSIT_ACCOUNT_REFRESH', onRefresh)
      paymentSocket.on('PAYMENT_DEPOSIT_FAILED_RETRY', onRetry)
    }

    const removeListeners = () => {
      paymentSocket.off('connect', onConnect)
      paymentSocket.off('PAYMENT_DEPOSIT_SUCCESS', onSuccess)
      paymentSocket.off('PAYMENT_DEPOSIT_FAILED', onFailed)
      paymentSocket.off('PAYMENT_DEPOSIT_ACCOUNT_REFRESH', onRefresh)
      paymentSocket.off('PAYMENT_DEPOSIT_FAILED_RETRY', onRetry)
    }

    setupListeners()

    return () => {
      isMounted = false
      clearTimeout(timeout)
      removeListeners()
      paymentSocket.disconnect()
    }
  }, [])

  return (
    <PaymentLoader />
  )
}

export default TrustlyStatus
