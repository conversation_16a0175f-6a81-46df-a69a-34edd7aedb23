import { makeStyles } from '@mui/styles'

import { checkMark, leftArrow, rightArrow } from '../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  landingPageWrap: {
    width: '100%',
    flexDirection: 'column',
    background: theme.colors.textBlack,
    '& .landing-banner-section': {
      // Optimized background styling for LCP/FCP performance
      position: 'relative',
      width: '100%',
      minHeight: '600px',
      backgroundImage: 'var(--banner-bg)',
      backgroundSize: 'cover',
      backgroundPosition: '100% -10%',
      backgroundRepeat: 'no-repeat',
      // Critical performance optimizations
      contain: 'layout style paint',
      willChange: 'transform',
      transform: 'translateZ(0)',
      backfaceVisibility: 'hidden',
      [theme.breakpoints.down('md')]: {
        minHeight: '500px',
      },
      [theme.breakpoints.down('sm')]: {
        minHeight: '400px',
        backgroundPosition: 'center center',
      },

      '& .landing-banner-content': {
        // Optimized positioning and layout for performance
        position: 'relative',
        zIndex: 2,
        width: '100%',
        height: '100%',
        padding: theme.spacing(11.5, 2, 0),
        textAlign: 'center',
        maxWidth: theme.spacing(45.25),
        margin: '0 auto',
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(10, 1, 2),
        },
        '& h1': {
          fontSize: theme.spacing(4.375),
          fontWeight: '900',
          lineHeight: 1.1,
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(2.125)
          },
          '& span': {
            color: theme.colors.YellowishOrange
          }
        },
        '& > p': {
          fontSize: theme.spacing(1.4),
          fontWeight: theme.typography.fontWeightMedium,
          marginBottom: theme.spacing(0.9375)
          // [theme.breakpoints.down('lg')]: {
          //   fontSize: theme.spacing(1.125)
          // }
        },
        '& .landiang-email-input': {
          marginBottom: theme.spacing(1.25),
          '& .MuiOutlinedInput-root': {
            borderRadius: theme.spacing(3.125),
            padding: theme.spacing(0, 1),
            backgroundColor: theme.colors.textWhite,
            minWidth: theme.spacing(28.125),
            margin: '0 auto',
            minHeight: theme.spacing(3.125),
            fontSize: `${theme.spacing(1.25)} !important`,
            fontWeight: theme.typography.fontWeightBold,
            [theme.breakpoints.down('md')]: {
              minWidth: '100%'
            },
            '& input': {
              padding: theme.spacing(0.313, 0.313),
              fontSize: theme.spacing(1.25),
              color: `${theme.colors.textBlack} !important`,
              '&::placeholder': {
                fontSize: `${theme.spacing(1.25)} !important`,
                fontWeight: `${theme.typography.fontWeightBold} !important`
              },
              '&:-webkit-autofill': {
                background: '#fff',
                boxShadow: '0 0 0 30px #fff inset !important',
                color: '#000 !important',
                border: 'none !important',
                caretColor: '#000',
                WebkitTextFillColor: '#000 !important',
                fontSize: '1.25rem !important',
                '&:focus': {
                  fontSize: '1.25rem !important'
                }
              }
            },
            '& fieldset': {
              border: 'none',
              fontSize: theme.spacing(1.25)
            },
            '&:hover fieldset': {
              border: 'none'
            }
          },
          '& .MuiSvgIcon-root': {
            color: theme.colors.homeEmail
          },
          '& .Mui-error': {
            fontSize: theme.spacing(1.125),
            textAlign: 'center',
            // color: theme.colors.textWhite
            color: 'red',
            fontWeight: `${theme.typography.fontWeightBold} !important`
          }
        },
        '& .btn-wrap': {
          paddingBottom: theme.spacing(0.625),
          '& .btn': {
            lineHeight: theme.spacing(2),
            '&.Mui-disabled': {
              opacity: '1',
              background: theme.colors.YellowishOrange,
              filter: 'unset',
              color: `${theme.colors.textBlack} !important`,
              pointerEvents: 'auto',
              '&:hover': {
                background: 'transparent !important',
                color: `${theme.colors.YellowishOrange} !important`
              }
            }
          }
        }
      },
      '& .provider-section': {
        '& .group-graphic': {
          maxWidth: theme.spacing(77.5),
          margin: '1rem auto 0',
          '& img': {
            width: '100%',
            height: 'auto',
            aspectRatio: '1239/556', // Actual GangImg aspect ratio
            display: 'block',
            [theme.breakpoints.down('md')]: {
              transform: 'scale(1.1)'
            }
          }
        },
        '& .provider-content-wrap': {
          background: theme.colors.cmsMOdalBg,
          padding: theme.spacing(1.875),
          borderRadius: theme.spacing(1.25),
          position: 'relative',
          top: theme.spacing(-5),
          minHeight: theme.spacing(12.25),
          maxWidth: theme.spacing(71.25),
          margin: '0 auto',
          display: 'flex',
          justifyContent: 'center',
          gap: theme.spacing(1),
          flexDirection: 'column',
          [theme.breakpoints.down('lg')]: {
            padding: theme.spacing(1.5, 1),
            minHeight: 'auto',
            top: theme.spacing(-4),
            zIndex: '1'
          },
          [theme.breakpoints.down('md')]: {
            top: theme.spacing(-2)
          },
          '& h3': {
            fontSize: theme.spacing(1.875),
            fontWeight: '900',
            textAlign: 'center',
            color: theme.colors.textWhite,
            [theme.breakpoints.down('lg')]: {
              fontSize: theme.spacing(1.375)
            },
            [theme.breakpoints.down('md')]: {
              maxWidth: theme.spacing(14.375),
              margin: '0 auto'
            }
          },

          '& .swiper-button-prev, & .swiper-button-next': {
            opacity: 1,
            height: 'auto',
            width: 'auto',
            top: '32%',
            left: '0',
            margin: '0',
            [theme.breakpoints.down('md')]: {
              top: '23%'
            },
            '&:after': {
              fontSize: '0',
              backgroundImage: `url(${leftArrow})`,
              content: "''",
              height: theme.spacing(1.25),
              width: theme.spacing(1.25),
              backgroundSize: '100%',
              [theme.breakpoints.down('md')]: {
                height: theme.spacing(1),
                width: theme.spacing(1)
              }
            }
          },
          '& .swiper-button-next': {
            left: 'auto',
            right: '0',
            '&:after': {
              backgroundImage: `url(${rightArrow}) !important`
            }
          },
          '& .provider-slider-content': {
            position: 'relative',

            '& .swiper': {
              padding: theme.spacing(0, 1.875),
              position: 'relative',
              '& .swiper-slide': {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: theme.spacing(7.5), // 60px minimum height
                '& figure': {
                  margin: '0',
                  aspectRatio: '5/3',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }
              },
              '& img': {
                width: theme.spacing(8.75),
                height: 'auto',
                aspectRatio: '5/3',
                objectFit: 'contain',
                [theme.breakpoints.down('md')]: {
                  width: theme.spacing(6)
                },
                [theme.breakpoints.down('sm')]: {
                  width: theme.spacing(4)
                },
                [theme.breakpoints.down(400)]: {
                  width: theme.spacing(3)
                }
              },

              '&:before, &:after': {
                position: 'absolute',
                background: theme.colors.cmsMOdalBg,
                // background: "red",
                content: "''",
                height: '2rem',
                width: '2rem',
                zIndex: '5',
                left: '0',
                top: '0'
              },
              '&:after': {
                left: 'auto',
                right: '0'
              }
            }
          }
        }
      }
    },
    '& .landing-banner-img': {
      position: 'relative',
      '&:before': {
        position: 'absolute',
        background: theme.colors.bannerGLow,
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        content: "''",
        height: '25rem',
        width: '25rem',
        borderRadius: '100%',
        filter: 'blur(80px)',
        [theme.breakpoints.down('md')]: {
          display: 'none'
        }
      },
      '& img': {
        height: '100%',
        width: '100%'
      }
    },
    '& .inner-heading': {
      marginBottom: theme.spacing(2),
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(0),
        marginBottom: theme.spacing(0)
      },
      '& h2': {
        fontSize: theme.spacing(2.75),
        fontWeight: '900',
        lineHeight: theme.spacing(3.5),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.875),
          lineHeight: theme.spacing(2.7),
          paddingTop: '1rem',
          marginBottom: theme.spacing(1)
        },
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1.875),
          paddingTop: '0rem',
          marginBottom: theme.spacing(0)
        },

        '& span': {
          color: theme.colors.YellowishOrange
        },
        '& br': {
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        }
      },
      '& h4': {
        fontSize: theme.spacing(2.75),
        fontWeight: '900',
        lineHeight: theme.spacing(3.5),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.875),
          lineHeight: theme.spacing(2.7),
          paddingTop: '1rem',
          marginBottom: theme.spacing(1)
        },
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1.875),
          paddingTop: '0rem',
          marginBottom: theme.spacing(0)
        },

        '& span': {
          color: theme.colors.YellowishOrange
        },
        '& br': {
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        }
      },
      '& h3': {
        color: theme.colors.textBlack,
        fontSize: theme.spacing(2),
        fontWeight: '900',
        lineHeight: theme.spacing(3.5),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.875),
          lineHeight: theme.spacing(2.7),
          paddingTop: '1rem',
          marginBottom: theme.spacing(1)
        },
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1.875),
          paddingTop: '0rem',
          marginBottom: theme.spacing(0)
        }
      },
      '& p': {
        fontSize: theme.spacing(1.375),
        fontWeight: '400',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1)
        }
      }
    },

    '& .inner-heading-why-section': {
      marginBottom: '7rem',
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(0),
        marginBottom: '7rem'
      },
      '& h4': {
        fontSize: theme.spacing(2.75),
        fontWeight: '900',
        lineHeight: theme.spacing(3.5),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.875),
          lineHeight: theme.spacing(2.7),
          paddingTop: '1rem',
          marginBottom: theme.spacing(1)
        },
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1.875),
          paddingTop: '0rem',
          marginBottom: theme.spacing(0)
        },

        '& span': {
          color: theme.colors.YellowishOrange
        },
        '& br': {
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        }
      }
    },
    '& .inner-container': {
      maxWidth: theme.spacing(71.25),
      margin: '0 auto'
    },
    '& .btn-primary': {
      '&.MuiButtonBase-root': {
        color: theme.colors.textBlack,
        fontSize: theme.spacing(1.25),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      }
    },
    '& .btn-secondary': {
      '&.MuiButtonBase-root': {
        fontSize: theme.spacing(1.25),
        color: theme.colors.YellowishOrange,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          color: theme.colors.textBlack
        }
      }
    },
    '& .free-content-wrap': {
      '& ul': {
        listStyle: 'none',
        paddingLeft: theme.spacing(2),
        '& li': {
          display: 'block',
          fontSize: theme.spacing(1.25),
          paddingBottom: theme.spacing(1.5),
          position: 'relative',
          color: theme.colors.textBlack,
          '&:before': {
            position: 'absolute',
            content: "''",
            background: `url(${checkMark})`,
            height: theme.spacing(1.25),
            width: theme.spacing(1.25),
            backgroundSize: theme.spacing(1.25),
            left: theme.spacing(-1.875),
            top: theme.spacing(0.1875),
            filter: 'brightness(0.1)'
          }
        }
      }
    },
    '& .footer-content-wrap': {
      [theme.breakpoints.down('md')]: {
        paddingBottom: '0 !important',
        paddingTop: '0 !important'
      }
    },
    '& .btn-wrap': {
      padding: theme.spacing(0, 0, 1),
      display: 'flex',
      gap: theme.spacing(1),
      justifyContent: 'center',
      '& .btn': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing(0.313)
      }
    },
    '& .banner-disclaimer': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: theme.spacing(0.625),
      [theme.breakpoints.down('md')]: {
        justifyContent: 'center'
      },
      [theme.breakpoints.down('lg')]: {
        gap: theme.spacing(0.2),
        alignItems: 'flex-start'
      },

      '& img': {
        width: theme.spacing(1)
      },
      '& p': {
        margin: '0',
        fontSize: theme.spacing(1.125),
        fontWeight: '400',
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(1.1)
        },
        [theme.breakpoints.down('md')]: {
          textAlign: 'center'
        }
      }
    },
    '& .cta-wrap': {
      // padding: theme.spacing(1., 0),
      '& .btn-wrap': {
        [theme.breakpoints.down('sm')]: {
          flexDirection: 'column',
          alignItems: 'center'
        },

        '& .btn': {
          width: 'auto'
        }
      }
    },
    '& .casino-card': {
      position: 'relative',
      paddingBottom: '140%',
      borderRadius: theme.spacing(0.625),
      overflow: 'hidden',
      cursor: 'pointer',

      '& > img': {
        width: '100%',
        height: '100%',
        position: 'absolute',
        left: '0',
        top: '0'
      },
      '&:hover': {
        '& .overlay-box': {
          display: 'flex'
        }
      }
    },
    '& .overlay-box': {
      position: 'absolute',
      width: '100%',
      height: '100%',
      background: theme.colors.overlayBg,
      display: 'none',
      flexDirection: 'column',
      textAlign: 'center',
      justifyContent: 'center',
      transition: 'all 500ms ease-in-out',
      '& img': {
        width: theme.spacing(5),
        margin: '0 auto',
        [theme.breakpoints.down('lg')]: {
          width: theme.spacing(2)
        }
      },
      '& p': {
        fontSize: theme.spacing(1),
        fontWeight: '700',
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(0.875)
        }
      }
    }
  }
}))
