import { makeStyles } from '@mui/styles'

import { contactBg } from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  contactSectionWrap: {
    background: `url(${contactBg})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    padding: theme.spacing(5, 1, 3.5),
    [theme.breakpoints.down('lg')]: {
      padding: theme.spacing(2, 1, 8)
    },
    '& .winning-box-wrap': {
      background: theme.colors.YellowishOrange,
      padding: theme.spacing(2),
      borderRadius: theme.spacing(1.25),
      '& .MuiGrid-container': {
        alignItems: 'center',
        '& .landiang-email-input': {
          marginBottom: theme.spacing(0),
          display: 'flex',
          justifyContent: 'center',
          '& .MuiTextField-root': {
            '& .MuiOutlinedInput-root': {
              borderRadius: theme.spacing(3.125),
              padding: theme.spacing(0, 1),
              backgroundColor: theme.colors.textWhite,
              minHeight: theme.spacing(3.125),
              fontSize: theme.spacing(1.25),
              fontWeight: theme.typography.fontWeightBold,
              '& input': {
                padding: theme.spacing(0.313, 0.313),
                fontSize: theme.spacing(1.25),
                color: `${theme.colors.textBlack} !important`,
                '&::placeholder': {
                  fontSize: `${theme.spacing(1.25)} !important`,
                  fontWeight: `${theme.typography.fontWeightBold} !important`
                }
              },
              '& fieldset': {
                border: 'none',
                fontSize: theme.spacing(1.25)
              },
              '&:hover fieldset': {
                border: 'none'
              }
            },
            '& .Mui-error': {
              fontSize: theme.spacing(1.125),
              textAlign: 'center',
              color: 'red',
              fontWeight: `${theme.typography.fontWeightBold} !important`
            }
          },
          '& .MuiSvgIcon-root': {
            color: theme.colors.homeEmail
          }
        },
        '& .winningl-left-content': {
          '& h4': {
            fontSize: theme.spacing(2.875),
            fontWeight: '900',
            color: theme.colors.textBlack,
            lineHeight: '1.3',
            [theme.breakpoints.down('lg')]: {
              fontSize: theme.spacing(2)
            }
          },
          '& p': {
            fontSize: theme.spacing(1.25),
            color: theme.colors.textBlack,
            lineHeight: '1.6',
            fontWeight: '400'
          }
        },
        '& .contact-graphic-wrap': {
          position: 'absolute',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -45.8%)',
          [theme.breakpoints.down('md')]: {
            transform: 'translate(-50%, -39.8%)'
          },
          '& img': {
            width: theme.spacing(29.875),
            [theme.breakpoints.down('md')]: {
              width: theme.spacing(18.375)
            }
          }
        },
        '& .winning-right-content': {
          [theme.breakpoints.down('md')]: {
            marginBottom: theme.spacing(6.25),
            textAlign: 'center'
          },

          '& p': {
            fontSize: theme.spacing(1.25),
            marginBottom: theme.spacing(1),
            fontWeight: '400',
            color: theme.colors.textBlack,
            textAlign: 'center'
          },
          '& .btn-wrap': {
            textAlign: 'center',
            marginTop: theme.spacing(0.625),
            '& .btn': {
              boxShadow: theme.shadows[23],
              borderColor: theme.colors.textBlack,
              borderWidth: '2px',
              '&:hover': {
                boxShadow: 'none',
                color: theme.colors.textBlack
              },
              '&.Mui-disabled': {
                opacity: '1',
                boxShadow: theme.shadows[23],
                borderColor: theme.colors.textBlack,
                filter: 'unset',
                color: `${theme.colors.textBlack} !important`,
                pointerEvents: 'auto',
                '&:hover': {
                  background: 'transparent !important',
                  color: `${theme.colors.textBlack} !important`,
                  boxShadow: 'none'
                }
              }
            }
          },
          '& .banner-disclaimer': {
            '& p': {
              fontSize: `${theme.spacing(1)} !important`,
              margin: '0 !important'
            }
          }
        },
        '& .order-1': {
          [theme.breakpoints.down('md')]: {
            order: 1
          }
        },
        '& .order-2': {
          [theme.breakpoints.down('md')]: {
            order: 2
          }
        },
        '& .order-3': {
          [theme.breakpoints.down('md')]: {
            order: 3
          }
        }
      }
    }
  }
}))
