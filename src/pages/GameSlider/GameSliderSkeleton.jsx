import React from 'react'
import { Box, Grid, Typography, useTheme } from '@mui/material'
import { GameSwiperWrapper } from './gameslider.styles'
import { Swiper, SwiperSlide } from 'swiper/react'
import { FreeMode, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'

const GameSliderSkeleton = ({ title = 'Loading Games...' }) => {
  const theme = useTheme()

  // Create skeleton game cards
  const skeletonCards = Array.from({ length: 6 }, (_, index) => (
    <SwiperSlide key={`skeleton-game-${index}`}>
      <Box
        sx={{
          position: 'relative',
          borderRadius: '8px',
          overflow: 'hidden',
          backgroundColor: '#333234',
          aspectRatio: '3/4', // Game card aspect ratio
          minHeight: '200px',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Game image skeleton */}
        <Box
          sx={{
            flex: 1,
            backgroundColor: '#444',
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent)',
              animation: 'shimmer 1.5s infinite'
            }
          }}
        />

        {/* Game title skeleton */}
        <Box
          sx={{
            padding: '8px',
            backgroundColor: '#333234'
          }}
        >
          <Box
            sx={{
              width: '80%',
              height: '12px',
              backgroundColor: '#555',
              borderRadius: '6px',
              margin: '0 auto'
            }}
          />
        </Box>

        {/* Favorite icon skeleton */}
        <Box
          sx={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            width: '24px',
            height: '24px',
            backgroundColor: '#555',
            borderRadius: '50%'
          }}
        />
      </Box>
    </SwiperSlide>
  ))

  return (
    <>
      <style>
        {`
          @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
          }
        `}
      </style>
      <GameSwiperWrapper theme={theme}>
        <section className='game-slider-section'>
          <Grid className='gameHeading'>
            <Grid className='heading'>
              <Typography
                sx={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  fontSize: '1.2rem',
                  fontWeight: '600'
                }}
              >
                {title}
              </Typography>
            </Grid>
          </Grid>

          <Box className='games-slider-wrap'>
            <Swiper
              grabCursor
              centeredSlides={false}
              loop={false}
              freeMode
              navigation={false}
              modules={[FreeMode, Navigation]}
              spaceBetween={15}
              className='mySwiper'
              breakpoints={{
                0: {
                  slidesPerView: 2.2,
                  spaceBetween: 10
                },
                768: {
                  slidesPerView: 3.5,
                  spaceBetween: 15
                },
                1024: {
                  slidesPerView: 4.5,
                  spaceBetween: 15
                },
                1200: {
                  slidesPerView: 5.5,
                  spaceBetween: 20
                }
              }}
            >
              {skeletonCards}
            </Swiper>
          </Box>
        </section>
      </GameSwiperWrapper>
    </>
  )
}

export default GameSliderSkeleton
