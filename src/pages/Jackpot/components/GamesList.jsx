import React, { useEffect, useState, useCallback } from 'react'
import useStyles from '../Jackpot.styles'
import {
  Box,
  CircularProgress,
  Grid,
  Tooltip,
  Typography
} from '@mui/material'
import CasinoCard from '../../../components/ui-kit/icons/utils/casinoGames.webp'
import whitePlay from '../../../components/ui-kit/icons/svg/white-play-button.svg'
import TournamentLogo from '../../../components/ui-kit/icons/png/tournament-logo.png'
import PragmaticJackpotSCLogo from '../../../components/ui-kit/icons/svg/pragmatic-sc.svg'
import PragmaticJackpotGCLogo from '../../../components/ui-kit/icons/svg/pragmatic-gc.svg'
import LazyImage from '../../../utils/lazyImage'
import { getItem, getLoginToken } from '../../../utils/storageUtils'
import { usePortalStore, useUserStore } from '../../../store/store'
import { usePragmaticJackpotStore } from '../../../store/usePragmaticJackpot'
import { formatPriceWithCommas, dynamicMerge } from '../../../utils/helpers'
import { CasinoQuery, useGetProfileMutation } from '../../../reactQuery'
import MobileVerification from '../../MobileVerification'

const GamesList = ({ loading, handlePlayNow }) => {
  const classes = useStyles()
  const coinType = getItem('coin')
  const auth = useUserStore((state) => state)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const portalStore = usePortalStore((state) => state)

  const [allGames, setAllGames] = useState([])
  const [gameId, setGameId] = useState(null)
  const [name, setName] = useState('')
  const [subCategoryName, setSubCategoryName] = useState('Lobby')
  const [gameDataCount, setGameDataCount] = useState(0)
  const [pageNo, setPageNo] = useState(1)
  const [gamesLoading, setGamesLoading] = useState(false)
  const [gameDataLoading, setGameDataLoading] = useState(false)
  const [limit] = useState(21)

  const { pragmaticJackpotSc, pragmaticJackpotGc } = usePragmaticJackpotStore()

  const successToggler = (res) => {
    const data = res?.data?.data?.[0]
    const games = data?.subCategoryGames || []
    const name = data?.name || 'Lobby'
    const count = data?.totalGames || 0

    setAllGames((prev) => dynamicMerge(prev, games, 'masterCasinoGameId'))
    setSubCategoryName(name)
    setGameDataCount(count)
    setGameDataLoading(false)
    setGamesLoading(false)
  }

  const subcategoryListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler: (err) => console.error('Fetch error:', err),
  })

  const handleLoadMore = useCallback(() => {
    if (Math.ceil(gameDataCount / limit) > pageNo) {
      const nextPage = pageNo + 1
      setPageNo(nextPage)
      setGameDataLoading(true)
      subcategoryListMutation.mutate({
        subCategorySlug: 'jackpot',
        limit,
        page: nextPage
      })
    }
  }, [gameDataCount, limit, pageNo, subcategoryListMutation])

  const handleScroll = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight
    const clientHeight = document.documentElement.clientHeight

    if (scrollTop + clientHeight >= scrollHeight - 200) {
      if (Math.ceil(gameDataCount / limit) > pageNo && !gameDataLoading) {
        handleLoadMore()
      }
    }
  }, [gameDataCount, limit, pageNo, gameDataLoading, handleLoadMore])

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (!res?.data?.data?.phoneVerified) {
        portalStore.openPortal(
          () => (
            <MobileVerification
              calledFor='gamePlay'
              handlePlayNow={() => handlePlayNow(gameId, name, subCategoryName)}
            />
          ),
          'innerModal'
        )
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const handlePhoneVerification = (gameId, name, subCategoryName) => {
    if (coinType === 'SC' && !auth?.userDetails?.phoneVerified && (getLoginToken() || auth.isAuthenticate)) {
      setGameId(gameId)
      setName(name)
      getProfileMutation.mutate()
    } else {
      handlePlayNow(gameId, name, subCategoryName)
    }
  }

  const renderJackpotValue = (gameId) => {
    const isSC = coinType === 'SC' && pragmaticJackpotSc?.[gameId]
    const isGC = coinType === 'GC' && pragmaticJackpotGc?.[gameId]
    const jackpot = isSC ? pragmaticJackpotSc?.[gameId] : isGC ? pragmaticJackpotGc?.[gameId] : null

    if (!jackpot) return null

    return (
      <div className='prgamatic-jackpot-amount-wrapper'>
        <LazyImage
          src={coinType === 'SC' ? PragmaticJackpotSCLogo : PragmaticJackpotGCLogo}
          alt='pragmatic-jackpot-logo'
        />
        <Typography
          style={{
            color: coinType === 'SC' ? '#00C80E' : '#FDB72E',
            fontWeight: 700,
            fontSize: 10
          }}
        >
          {formatPriceWithCommas(jackpot)} {coinType}
        </Typography>
      </div>
    )
  }

  useEffect(() => {
    subcategoryListMutation.mutate({
      subCategorySlug: 'jackpot',
      limit,
      page: pageNo
    })
  }, [])

  return (
    <>
      <Box className={!loading ? 'jackpot-games-wrap' : ''}>
        {allGames.map((game, key) => {
          const gameId = String(game?.masterCasinoGameId)

          return (
            <div className='custom-col-2' key={`${gameId}_${key}`}>
              <Tooltip
                title={game?.gameInTournament ? 'TOURNAMENT' : ''}
                arrow
                disableHoverListener={!game?.gameInTournament}
                placement='top-start'
                componentsProps={{
                  tooltip: {
                    sx: {
                      backgroundColor: '#FF3000',
                      color: 'white',
                      fontWeight: '800',
                      fontSize: '14px',
                      textAlign: 'center'
                    }
                  },
                  arrow: {
                    sx: {
                      color: '#FF3000',
                      left: '16px',
                      mt: '-8px'
                    }
                  }
                }}
              >
                <Grid className='casino-card'>
                  <img
                    src={game?.imageUrl || CasinoCard}
                    alt={game?.name}
                    className='casinoGame-img'
                    loading='lazy'
                  />
                  <Grid
                    className='casino-overlay'
                    onClick={() =>
                      handlePhoneVerification(game?.masterCasinoGameId, game?.name, subCategoryName)
                    }
                  >
                    <Typography
                      variant='h6'
                      sx={{ lineHeight: '20px', textAlign: 'center', padding: '0 10px' }}
                    >
                      <b>{game.name}</b>
                    </Typography>
                    <img src={whitePlay} alt='Play' className={classes.playImg} />
                    <b>Play Now</b>
                  </Grid>

                  {renderJackpotValue(gameId)}
                  {game?.gameInTournament && (
                    <img src={TournamentLogo} alt='tournament-logo' className='tournamentLogo' />
                  )}
                </Grid>
              </Tooltip>
            </div>
          )
        })}
      </Box>

      {gameDataCount === 0 && (
        <Grid className='no-data-content'>
          <Typography sx={{ textAlign: 'center' }}>No Games Found</Typography>
        </Grid>
      )}

      {gamesLoading || gameDataLoading
        ? (
          <Grid className={classes.loadMore}>
            <CircularProgress size={25} />
          </Grid>)
        : null}
    </>
  )
}

export default GamesList
