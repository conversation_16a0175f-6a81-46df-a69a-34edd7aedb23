import { makeStyles } from '@mui/styles'

import { PromotionBanner } from '../../components/ui-kit/icons/banner'
import { LobbyRight, ButtonPrimary } from '../../MainPage.styles'
export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),

    '& .inner-banner-wrap': {
      marginBottom: theme.spacing(4.125),
      [theme.breakpoints.down('md')]: {
        marginBottom: theme.spacing(2.125)
      },
      '& .promotion-banner': {
        background: `url(${PromotionBanner})`,
        backgroundSize: 'cover',
        minHeight: theme.spacing(20),
        padding: theme.spacing(2),
        borderRadius: theme.spacing(1.875),
        boxShadow: theme.shadows[13],
        [theme.breakpoints.down('lg')]: {
          minHeight: theme.spacing(12)
        },
        // [theme.breakpoints.down('md')]: {
        //   minHeight: theme.spacing(9),
        // },
        [theme.breakpoints.down('md')]: {
          backgroundPosition: '72% 100%'
          // display: "none"
        },
        [theme.breakpoints.down('sm')]: {
          backgroundPosition: '77% 100%',
          padding: ' 2rem 1rem'
        },
        '& .banner-content': {
          marginLeft: theme.spacing(5.5),
          marginTop: theme.spacing(1.5),
          [theme.breakpoints.down('lg')]: {
            marginLeft: 0,
            marginTop: 0
          },
          '& h2': {
            fontSize: theme.spacing(4.375),
            fontWeight: '700',
            [theme.breakpoints.down('lg')]: {
              fontSize: theme.spacing(2)
            },

            '& span': {
              color: theme.colors.YellowishOrange,
              display: 'block'
            }
          },
          '& p': {
            fontWeight: '700',
            color: theme.colors.textWhite,
            fontSize: theme.spacing(1.4375),
            textTransform: 'uppercase',
            maxWidth: theme.spacing(25),
            [theme.breakpoints.down('lg')]: {
              fontSize: theme.spacing(0.875)
            }
          }
        }
      },

      '& .MuiButtonBase-root': {
        color: theme.colors.textBlack,
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      }
    }
  },
  giveawaySection: {
    padding: theme.spacing(2, 0),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(0.625, 0, 0)
    },
    '& .giveaway-counter-wrap': {
      border: `1px solid ${theme.colors.giveAwayBorder}`,
      borderRadius: theme.spacing(0.625),
      padding: theme.spacing(1.25),
      textAlign: 'center',
      boxShadow: theme.shadows[20],
      height: theme.spacing(16.668),
      [theme.breakpoints.down('lg')]: {
        height: 'auto'
      },

      '& .giveaway-badge': {
        border: `1px solid ${theme.colors.giveAwayBorder}`,
        borderRadius: theme.spacing(1.875),
        fontSize: theme.spacing(1.625),
        fontWeight: theme.typography.fontWeightBold,
        color: theme.colors.textWhite,
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: theme.spacing(0.625, 2.5),
        gap: theme.spacing(1.0625),
        marginBottom: theme.spacing(1.5),
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(1)
        },
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.9375)
        },
        '& span': {
          height: theme.spacing(0.875),
          width: theme.spacing(0.875),
          borderRadius: '100%'
        },
        '&.upcoming-badge': {
          '& span': {
            background: theme.colors.YellowishOrange
          }
        },
        '&.live-badge': {
          '& span': {
            background: theme.colors.greenText
          }
        },
        '&.ended-badge': {
          '& span': {
            background: theme.colors.endedBadge
          }
        }
      }
    },
    '& .giveaway-counter-content': {
      display: 'flex',
      justifyContent: 'center',
      gap: theme.spacing(1),
      [theme.breakpoints.down('md')]: {
        gap: theme.spacing(0.5)
      },
      '& .giveaway-counter': {
        height: theme.spacing(6.5625),
        width: theme.spacing(6.5625),
        borderRadius: '100%',
        background: theme.colors.counterGradient,
        padding: theme.spacing(1),
        boxShadow: theme.shadows[21],
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        [theme.breakpoints.down(1460)]: {
          height: theme.spacing(5.125),
          width: theme.spacing(5.125)
        },
        [theme.breakpoints.down('md')]: {
          height: theme.spacing(4.125),
          width: theme.spacing(4.125)
        },
        [theme.breakpoints.down('sm')]: {
          height: theme.spacing(4),
          width: theme.spacing(4)
        },
        '& .giveaway-counter-progress': {
          margin: '0 20px',
          width: theme.spacing(5),
          height: theme.spacing(5),
          borderRadius: '5px',
          '& .percent': { position: 'relative', width: theme.spacing(5), height: theme.spacing(5) },
          '& svg': {
            transform: 'rotate(-90deg)',
            width: '100%',
            height: '100%',
            '& circle': {
              fill: 'none',
              strokeWidth: 6,
              stroke: '#e6e6e6',
              cx: '40',
              cy: '40',
              r: '36',
              '&:last-of-type': {
                stroke: '#4caf50',
                strokeDasharray: '226.2',
                strokeDashoffset: 'calc(226.2 - (226.2 * var(--percent)) / 100)',
                transition: 'stroke-dashoffset 0.5s ease'
              }
            }
          },
          '& > div': {
            [theme.breakpoints.down('md')]: {
              transform: 'scale(0.65) !important'
            }
          }
        },

        '& > svg': {
          position: 'relative',
          height: theme.spacing(5),
          width: theme.spacing(5),
          minWidth: theme.spacing(5),
          '& circle': {
            width: '100%',
            height: '100%',
            fill: 'none',
            stroke: '#f0f0f0',
            strokeWidth: 10,
            strokeLinecap: 'round',
            '&:last-of-type': {
              strokeDasharray: '625px',
              strokeDashoffset: 'calc(625px - (625px * var(--percent)) / 100)',
              stroke: '#3498db'
            }
          }
        }
      },
      '& .giveaway-counter-text': {
        '&  h4': {
          fontSize: theme.spacing(1.25),
          fontWeight: theme.typography.fontWeightBold,
          marginTop: theme.spacing(1),
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(0.875)
          },
          [theme.breakpoints.down('sm')]: {
            // fontSize: theme.spacing(0.875),
            marginTop: theme.spacing(0.625)
          }
        }
      },
      '& .live-counter-bg': {
        position: 'relative',
        '& .live-counter-text': {
          position: 'absolute',
          width: '100%',
          height: '100%',
          transform: 'translate(-50%, -50%)',
          left: '50%',
          top: '46%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          '& h4': {
            fontWeight: theme.typography.fontWeightExtraBold,
            fontSize: theme.spacing(1.875),

            [theme.breakpoints.down(1400)]: {
              fontSize: theme.spacing(1.25)
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(1),
              lineHeight: theme.spacing(0.825)
            }
          },
          '& p': {
            fontWeight: theme.typography.fontWeightBold,
            fontSize: theme.spacing(0.875),
            [theme.breakpoints.down(1400)]: {
              fontSize: theme.spacing(0.8)
            },
            [theme.breakpoints.down('md')]: {
              fontSize: theme.spacing(0.925),
              lineHeight: '1'
            },
            // [theme.breakpoints.down('sm')]: {
            //   fontSize: theme.spacing(0.875),
            //   lineHeight: '1'
            // },
            [theme.breakpoints.down(400)]: {
              fontSize: theme.spacing(0.625)
            }
          }
        },
        '& img': {
          width: theme.spacing(6.5625),
          height: theme.spacing(6.5625),
          [theme.breakpoints.down(1400)]: {
            width: theme.spacing(5.0625),
            height: theme.spacing(5.0625)
          },
          [theme.breakpoints.down('lg')]: {
            width: theme.spacing(5.0625),
            height: theme.spacing(5.0625)
          },
          [theme.breakpoints.down('md')]: {
            width: '100%',
            height: '100%'
          }
        }
      }
    },
    //  LINER PROGRESS
    '& .line-progress-wrap': {
      '& .line-progress-top-content': {
        display: 'flex',
        justifyContent: 'space-between',
        '& p': {
          fontSize: theme.spacing(1.25),
          fontWeight: theme.typography.fontWeightBold,
          color: theme.colors.textWhite,

          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1)
          }
        }
      },

      '&.progress-mob': {
        display: 'none',
        marginTop: theme.spacing(1),
        [theme.breakpoints.down('md')]: {
          display: 'block'
        }
      },
      '&.progress-web': {
        display: 'block',

        [theme.breakpoints.down('md')]: {
          display: 'none'
        }
      }
    },
    '& .show-ticket-section': {
      padding: theme.spacing(1, 0, 0),
      display: 'flex',
      justifyContent: 'space-between',
      // flexDirection:'column',
      gap: theme.spacing(0.625),
      [theme.breakpoints.down('md')]: {
        flexDirection: 'column',
        gap: 0,
        paddingTop: '0'
      },
      '& .show-ticket-cta': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'column',
        marginBottom: theme.spacing(0.625),
        [theme.breakpoints.down('md')]: {
          flexDirection: 'column',
          gap: theme.spacing(0.625)
        },

        '& .MuiButtonBase-root': {
          minHeight: theme.spacing(3.125),
          padding: theme.spacing(0.625, 2),
          minWidth: theme.spacing(17.1875),
          fontSize: theme.spacing(1.25),
          marginBottom: theme.spacing(0.625),
          [theme.breakpoints.down(1460)]: {
            // minWidth: "auto",
            minWidth: theme.spacing(15),
            padding: theme.spacing(0.625, 1),
            fontSize: theme.spacing(1)
            // minWidth: theme.spacing(11.875),
          },

          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1),
            minHeight: theme.spacing(1.625),
            lineHeight: theme.spacing(1),
            marginBottom: theme.spacing(0)
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.875),
            minWidth: theme.spacing(11)
          },
          '&.btn-stream': {
            border: `1px solid ${theme.colors.giveAwayBorder}`,
            borderRadius: theme.spacing(1.875),
            fontSize: theme.spacing(1.1875),
            color: theme.colors.textWhite,
            display: 'flex',
            gap: theme.spacing(1),
            marginBottom: theme.spacing(0.625),
            [theme.breakpoints.down(1460)]: {
              minWidth: 'auto',
              padding: theme.spacing(0.625, 1),
              fontSize: theme.spacing(1)
            },
            [theme.breakpoints.down('md')]: {
              fontSize: theme.spacing(1),
              minHeight: theme.spacing(1.625),
              marginBottom: theme.spacing(0),
              lineHeight: theme.spacing(1),
              '& img': {
                width: theme.spacing(3)
              }
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(0.875),
              minWidth: theme.spacing(11)
            },
            '& .kick-link': {
              textDecoration: 'none',
              color: 'inherit',
              display: 'flex',
              alignItems: 'center',
              gap: '1rem'
            },
            '& .kick-color': {
              textDecoration: 'none',
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              color: '#00A30B'
            }
          }
        },
        '& a': {
          fontWeight: theme.typography.fontWeightBold,
          fontSize: theme.spacing(1.25),
          // marginTop:theme.spacing(0.625),
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1)
            // marginTop:theme.spacing(0),
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.875)
          }
        },
        '& p': {
          fontSize: theme.spacing(0.9375),
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1)
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.875)
          }
        },
        '&.kick-mob-btn': {
          [theme.breakpoints.down('md')]: {
            flexDirection: 'column-reverse'
          }
        }
      }
    },
    '& .show-ticket-section2': {
      '& .tune-in': {
        paddingLeft: '5px',
        color: '#00A30B',
        fontWeight: 700,
        textAlign: 'center',
        fontSize: '16px',
        marginBottom: '10px',
        [theme.breakpoints.down('sm')]: {
          fontSize: '14px'
        }
      }
    },
    '& .prize-pool-section': {
      margin: theme.spacing(0, 0, 1),
      border: `1px solid ${theme.colors.giveAwayBorder}`,
      borderRadius: theme.spacing(1.25),
      padding: theme.spacing(2),
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(1)
      },
      '& .prize-pool-grid': {
        display: 'flex',
        justifyContent: 'space-between',
        flexDirection: 'column',
        // gridTemplateColumns: 'repeat(3, 1fr)',
        [theme.breakpoints.down(1460)]: {
          gridTemplateColumns: 'repeat(1, 1fr)'
        },
        '& .prize-pool-card': {
          position: 'relative',
          // '&:after': {
          //   position: 'absolute',
          //   right: theme.spacing(0.875),
          //   top: '50%',
          //   transform: 'translate(-50%, -50%)',
          //   width: '1px',
          //   height: '70%',
          //   background: theme.colors.prizePoolBorder,
          //   content: "''",
          //   [theme.breakpoints.down(1460)]: {
          //     display: 'none'
          //   }
          // },
          '&:last-child': {
            '&:after': {
              display: 'none'
            }
          },
          '& h4': {
            fontSize: theme.spacing(1.5625),
            fontWeight: theme.typography.fontWeightBold,
            // [theme.breakpoints.down(1460)]: {
            textAlign: 'center',
            marginTop: theme.spacing(0),
            // },
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(0.875)
            }
          },
          '& p': {
            fontWeight: theme.typography.fontWeightBold,
            fontSize: theme.spacing(1.2),
            // [theme.breakpoints.down(1460)]: {
            textAlign: 'center',
            border: `1px solid ${theme.colors.giveAwayBorder}`,
            margin: theme.spacing(0.313, 0),
            borderRadius: theme.spacing(0.625),
            padding: theme.spacing(0.313),
            // },
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(0.75),
              textAlign: 'center'
            }
          },
          '& .gold-coin-text': {
            color: theme.colors.YellowishOrange
          },
          '& .shweep-coin-text': {
            color: theme.colors.greenText
          }
        }
      }
    },
    '& .inner-heading': {
      marginBottom: theme.spacing(1.5),
      [theme.breakpoints.down('md')]: {
        textAlign: 'center'
      },
      '& h4': {
        fontSize: theme.spacing(1.875),
        fontWeight: theme.typography.fontWeightExtraBold,
        color: theme.colors.YellowishOrange
      }
    },
    '& .MuiGrid-container': {
      alignItems: 'center',
      '& .give-away-order-1': {
        [theme.breakpoints.down('md')]: {
          order: '1'
        }
      },
      '& .give-away-order-2': {
        [theme.breakpoints.down('md')]: {
          order: '2'
        }
      },
      '& .give-away-order-3': {
        [theme.breakpoints.down('md')]: {
          order: '3'
        }
      }
    }
  },
  referralSection: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    background: 'radial-gradient(ellipse at center, rgba(41,57,55,1) 0%,rgba(52,89,85,1) 50%,rgba(41,57,55,1) 100%)',
    borderRadius: theme.spacing(0.625),
    padding: theme.spacing(1),
    [theme.breakpoints.down('lg')]: {
      flexDirection: 'column !important'
    },
    '& .MuiTypography-h4': {
      fontSize: theme.spacing(2),
      fontWeight: theme.typography.fontWeightExtraBold,
      color: theme.colors.YellowishOrange,
      textTransform: 'capitalize'
    },
    '& .count-wrap': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: theme.spacing(0.625),
      '& .count-card': {
        borderWidth: theme.spacing(0.25, 0),
        borderStyle: 'solid',
        borderColor: theme.colors.timerBorder,
        background: theme.colors.timerBg,
        borderRadius: theme.spacing(0.25),
        padding: theme.spacing(0.25, 0),
        minWidth: theme.spacing(3.375),
        minHeight: theme.spacing(2.75),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        '& .MuiTypography-body1': {
          color: theme.colors.counterText,
          fontSize: theme.spacing(1.25),
          fontWeight: theme.typography.fontWeightExtraBold
        }
      },
      [theme.breakpoints.down('lg')]: {
        margin: theme.spacing(1, 0)
      }
    },
    '& .claim-btn': {
      ...ButtonPrimary(theme),
      color: theme.colors.textBlack,
      borderStyle: 'solid',
      borderColor: theme.colors.YellowishOrange,
      borderWidth: '1px',
      '&:hover': {
        background: 'transparent',
        color: theme.colors.YellowishOrange
      }
    },
    '& .counter-divider': {
      padding: theme.spacing(0, 0.625),
      marginTop: theme.spacing(0.625)
    }
  },
  referralViewSection: {
    background: theme.colors.coinBundle,
    borderRadius: '12px',
    padding: theme.spacing(1.25, 3.0625, 1.25, 1.25),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1)
    },
    '& .raffleimg': {
      width: '210px',
      [theme.breakpoints.down('md')]: {}
    },
    '& .claim-btn': {
      ...ButtonPrimary(theme),
      color: theme.colors.textBlack,
      borderStyle: 'solid',
      borderColor: theme.colors.YellowishOrange,
      borderWidth: '1px',
      minWidth: theme.spacing(14.3125),
      minHeight: theme.spacing(3.25),
      fontSize: theme.spacing(1.25),
      fontWeight: '600',
      '&:hover': {
        background: 'transparent',
        color: theme.colors.YellowishOrange
      }
    },
    '& .mainBox': {
      display: 'flex',
      gap: theme.spacing(5.8)
    },
    '& .boxOne': {
      textAlign: 'center',
      [theme.breakpoints.down('lg')]: {
        display: 'none'
      },
      '& img': {
        width: theme.spacing(18.7)
      },
      '& .weekly-raffle': {
        color: theme.colors.textWhite,
        fontWeight: '700',
        fontSize: theme.spacing(1.5625),
        marginTop: theme.spacing(1),
        marginBottom: theme.spacing(0.313)
      }
    },
    '& .boxTwo': {
      width: '100%'
    },
    '& .text-box-wrap': {
      display: 'flex',
      marginTop: theme.spacing(1.5625),
      gap: theme.spacing(1),
      alignItems: 'center',
      [theme.breakpoints.down('sm')]: {
        gap: theme.spacing(0.313)
      },
      [theme.breakpoints.down(375)]: {
        flexDirection: 'column'
      },
      '& .txtBox': {
        gap: theme.spacing(0.625),
        display: 'flex',
        '& h3': {
          margin: '0',
          fontSize: theme.spacing(1),
          color: theme.colors.Promosuccess,
          fontWeight: '600',
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.75)
          }
        }
      },
      '& span': {
        [theme.breakpoints.down(375)]: {
          display: 'none'
        }
      }
    },
    '& .referralSectionViewMore': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderRadius: theme.spacing(0.625),
      textTransform: 'capitalize',
      padding: theme.spacing(1, 0),
      [theme.breakpoints.down('lg')]: {
        flexDirection: 'column !important'
      },
      '& .MuiTypography-h4': {
        fontSize: theme.spacing(2),
        fontWeight: theme.typography.fontWeightExtraBold,
        color: theme.colors.YellowishOrange
      },
      '& .count-wrap': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing(0.625),
        '& .count-card': {
          fontSize: '1.200rem'
        },
        [theme.breakpoints.down('lg')]: {
          margin: theme.spacing(1, 0)
        }
      },
      '& .claim-btn': {
        ...ButtonPrimary(theme),
        color: theme.colors.textBlack,
        borderStyle: 'solid',
        borderColor: theme.colors.YellowishOrange,
        borderWidth: '1px',
        marginLeft: '0',
        '&:hover': {
          background: 'transparent',
          color: theme.colors.YellowishOrange
        }
      },
      '& .counter-divider': {
        padding: theme.spacing(0, 0.625),
        marginTop: theme.spacing(0.625)
      }
    },
    '& .progressbarViewMore': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderRadius: theme.spacing(0.625),
      [theme.breakpoints.down('lg')]: {
        flexDirection: 'column !important'
      },
      '& .MuiTypography-h4': {
        fontSize: theme.spacing(1.4375),
        fontWeight: '600',
        color: theme.colors.white
      },
      '& .count-wrap': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing(0.625),
        '& .MuiTypography-body1': {
          fontSize: theme.spacing(2),
          fontWeight: theme.typography.fontWeightSemiBold
        }
      }
    },
    '& .description': {
      fontSize: theme.spacing(0.75),
      fontWeight: '400',
      lineHeight: '16.59px',
      textAlign: 'left',
      textTransform: 'capitalize'
    },
    '& .headingText': {
      marginTop: '20px',
      marginBottom: theme.spacing(0.625),
      fontSize: theme.spacing(1),
      fontWeight: 700,
      lineHeight: '16.59px',
      textAlign: 'left',
      textTransform: 'capitalize'
    },
    '& .MuiLinearProgress-root': {
      width: '100%',
      height: '16px',
      backgroundColor: '#2b2b2b',
      borderRadius: '8px',
      '& .MuiLinearProgress-bar': {
        backgroundColor: '#FDB72E',
        borderRadius: '8px'
      }
    }
  },

  wrapper: {
    maxWidth: '75rem',
    margin: '0 auto',
    width: '100%',
    // "& .MuiTypography-root": {
    //
    // },
    [theme.breakpoints.down(576)]: {
      gap: '16px !important'
    },
    '&  .becomePartner': {
      ...ButtonPrimary(theme),
      padding: '15px 30px',
      textDecoration: 'none',
      marginTop: '20px',
      display: 'inline-block',
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        padding: '15px 30px'
      },
      [theme.breakpoints.down('sm')]: {
        padding: '8px 16px',
        marginTop: '10px'
      }
    },
    '& .banner': {
      position: 'relative',
      '& img': {
        width: '100%',
        borderRadius: theme.spacing(1.25),
        '&.img-1': {
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        },
        '&.img-2': {
          display: 'none',
          [theme.breakpoints.down('sm')]: {
            display: 'block'
          }
        }
      },
      '& .bannerText': {
        '& p': {
          fontSize: '32px',
          fontWeight: 'bold',
          color: '#000',
          [theme.breakpoints.down('lg')]: {
            fontSize: '19px'
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: '14px'
          }
        },
        position: 'absolute',
        top: '0',
        bottom: '0',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
        left: '2.8125rem',
        [theme.breakpoints.down('sm')]: {
          left: '1.5625rem'
        }
      }
    },
    '& .counter-section': {
      '& .MuiGrid-container': {
        alignItems: 'center'
      },
      '& .counter-weekly': {
        [theme.breakpoints.down('md')]: {
          textAlign: 'center'
        },
        '& h4': {
          fontSize: theme.spacing(1.875),
          fontWeight: '700',
          textTransform: 'capitalize',
          lineHeight: theme.spacing(5.0625),
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        },
        '& .MuiButtonBase-root': {
          fontSize: theme.spacing(1.25),
          fontWeight: '500',
          color: theme.colors.textBlack,
          minWidth: theme.spacing(14.25),
          minHeight: theme.spacing(3.25),

          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(0.875)
          },

          '&:hover': {
            color: theme.colors.YellowishOrange
          }
        }
      },

      '& .counter-grapic-wrap': {
        position: 'relative',
        [theme.breakpoints.down('md')]: {
          textAlign: 'center'
        },

        '& img': {
          width: '100%',
          [theme.breakpoints.down('md')]: {
            width: '60%'
          }
        }
      },
      '& .counter-wrap': {
        display: 'flex',
        justifyContent: 'flex-end',
        gap: theme.spacing(0.5),
        [theme.breakpoints.down('lg')]: {
          justifyContent: 'center'
        },
        '& .counter-card': {
          background: theme.colors.counterBg,
          color: theme.colors.textWhite,
          fontSize: theme.spacing(2.1875),
          fontWeight: '700',
          textAlign: 'center',
          fontStyle: 'italic',
          padding: theme.spacing(1),
          transform: 'skewX(-20deg)',
          boxShadow: theme.shadows[15],
          minWidth: theme.spacing(7.125),
          minHeight: theme.spacing(4.25),
          [theme.breakpoints.down('xl')]: {
            fontSize: theme.spacing(1.8),
            minWidth: theme.spacing(5.5)
          },
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(1.5625),
            minWidth: theme.spacing(5)
          }
        },
        '& .counter-card-value': {
          minWidth: theme.spacing(7.125),
          textAlign: 'center',
          fontSize: theme.spacing(1),
          fontWeight: '500',
          marginTop: theme.spacing(0.313),
          [theme.breakpoints.down('xl')]: {
            minWidth: theme.spacing(5.5)
          },
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(0.875),
            minWidth: theme.spacing(5)
          }
        }
      },
      '&.counter-responsive': {
        display: 'none',
        [theme.breakpoints.down('md')]: {
          display: 'block'
        }
      }
    }
  },

  promotionItems: {
    gap: theme.spacing(0.25),
    background: theme.colors.promotionBg,
    borderRadius: theme.spacing(1.25),
    padding: theme.spacing(1),
    boxShadow: theme.shadows[16],
    minHeight: theme.spacing(14.125),
    flexDirection: 'column',
    height: '100%',
    '& .promotionCountDownTimer': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },

    '& .promotionInner': {
      display: 'flex',
      alignItems: 'center',
      // justifyContent: 'space-between',

      width: '100%',
      gap: theme.spacing(3.75),
      [theme.breakpoints.down('md')]: {
        // flexDirection: 'column',
        justifyContent: 'space-between',

        gap: theme.spacing(1.25)
      }
    },

    '& .promotion-img': {
      '& img': {
        maxWidth: '10.4375rem',
        width: '100%',
        [theme.breakpoints.down('md')]: {
          maxWidth: '5.5rem'
        }
      }
    },

    '& .promotion-text': {
      padding: theme.spacing(0.5),
      [theme.breakpoints.down('lg')]: {
        padding: theme.spacing(1, 0)
      },
      [theme.breakpoints.down('md')]: {
        // textAlign: 'center',
        padding: theme.spacing(0.25, 0)
      },
      '& h3': {
        fontSize: theme.spacing(2.065),
        lineHeight: theme.spacing(2.6319),
        fontWeight: '700',
        color: theme.colors.YellowishOrange,
        paddingBottom: theme.spacing(0.25),

        [theme.breakpoints.down(1280)]: {
          fontSize: theme.spacing(1.375),
          paddingBottom: 0
        },

        [theme.breakpoints.down(1080)]: {
          fontSize: theme.spacing(1.25)
        }
      },

      '& p': {
        fontSize: theme.spacing(1.25),
        fontWeight: '400',
        maxWidth: '21.875rem',
        lineHeight: theme.spacing(1.4356),
        marginBottom: '0.75rem',

        [theme.breakpoints.down(1280)]: {
          fontSize: theme.spacing(1)
        },

        [theme.breakpoints.down(1080)]: {
          fontSize: theme.spacing(1)
          // margin: theme.spacing(0.625, 0)
        }
      },
      '& .counter-text': {
        fontWeight: 900,
        marginTop: '12px',
        fontSize: '15px'
      },
      '& .yellow-text': {
        color: '#FDB72E',
        fontWeight: 600,
        margin: '0',
        marginBottom: '0.75rem'
      },
      '& .green-text': {
        color: '#00A30B',
        fontWeight: 600,
        margin: '0',
        marginBottom: '0.75rem'
      }
    },

    [theme.breakpoints.down(1080)]: {
      padding: theme.spacing(0.9, 1),
      minHeight: theme.spacing(10.5625)
    },
    '& .claim-btn': {
      ...ButtonPrimary(theme),
      color: theme.colors.textBlack,
      // borderWidth: theme.spacing(0.25, 0),
      borderStyle: 'solid',
      borderColor: theme.colors.YellowishOrange,
      borderWidth: '1px',
      '&:hover': {
        background: 'transparent',
        color: theme.colors.YellowishOrange
      }
    },
    '& .instruction-text': {
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1)
      }
    }
  },
  bonusModalWrap: {
    minWidth: theme.spacing(15.625),
    background: '#262525',
    padding: '20px',
    position: 'relative',
    borderRadius: '0',

    '& .modal-close': {
      position: 'absolute',
      right: theme.spacing(1.25),
      top: theme.spacing(1),
      background: '#262525',
      height: theme.spacing(1.375),
      width: theme.spacing(1.375),
      borderRadius: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 2,
      '& .MuiButtonBase-root': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '0',
        margin: '0',
        position: 'relative',
        '& svg': {
          height: theme.spacing(1),
          width: theme.spacing(1),
          transform: 'translate(-50%, -50%)',
          top: '50%',
          left: '50%',
          position: 'absolute'
        }
      }
    },
    '& .MuiDialogContent-root': {
      background: 'none'
    },
    '& .title': {
      fontWeight: '700',
      textAlign: 'center'
    },
    '& .table-headings': {
      flex: '1',
      color: 'white',
      fontSize: '18px',
      fontWeight: theme.typography.fontWeightBold
    },
    '& .MuiTableHead-root': {
      '& .MuiTableCell-root': {
        fontWeight: theme.typography.fontWeightSemiBold,
        padding: theme.spacing(0.625),
        border: 'none',
        background: '#332c2c',
        '&:first-child': {
          borderRadius: theme.spacing(0.313, 0, 0, 0)
        },
        '&:last-child': {
          borderRadius: theme.spacing(0, 0.313, 0, 0)
        }
      }
    },
    '& .MuiTableBody-root': {
      '& .MuiTableRow-root': {
        '&:nth-child(odd)': {
          background: '#1b1a1a'
        }
      },
      '& .MuiTableCell-root': {
        border: 'none'
      }
    },
    '& .leaderBoardContainer-wrap': {
      maxHeight: '400px',
      overflowY: 'auto'
    }
  }
}))
