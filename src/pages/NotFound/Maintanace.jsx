import React, { useState, useEffect } from 'react'
import { Box, Button, Grid, Typography } from '@mui/material'
import './NotFound.scss'
import MaintenanceWrapper from './style.js'
import maintenanceImg from '../../components/ui-kit/icons/webp/maintenance.webp'
import useIntercom from '../../components/SideBar/hooks/useIntercom'

const Maintenance = ({ initialMinutes }) => {
  const { showIntercom } = useIntercom(false, true)
  const localStorageKey = 'maintenanceCountdownEndTime'

  // Function to calculate the remaining time
  const calculateRemainingTime = () => {
    const storedEndTime = localStorage.getItem(localStorageKey)
    const currentTime = Math.floor(Date.now() / 1000) // Current time in seconds

    if (storedEndTime) {
      const remainingTime = parseInt(storedEndTime, 10) - currentTime
      return remainingTime > 0 ? remainingTime : 0
    }

    return initialMinutes > 0 ? Math.floor(initialMinutes * 60) : 0
  }

  const [remainingTime, setRemainingTime] = useState(calculateRemainingTime)
  const [lastInitialMinutes, setLastInitialMinutes] = useState(initialMinutes)

  useEffect(() => {
    if (remainingTime === 0) return
    const currentTime = Math.floor(Date.now() / 1000)

    const newEndTime = currentTime + Math.floor(initialMinutes * 60)

    const storedEndTime = localStorage.getItem(localStorageKey)

    if (!storedEndTime || initialMinutes < lastInitialMinutes) {
      localStorage.setItem(localStorageKey, newEndTime)
      setRemainingTime(calculateRemainingTime())
    }

    setLastInitialMinutes(initialMinutes)
  }, [initialMinutes])

  useEffect(() => {
    if (remainingTime === 0) {
      localStorage.removeItem(localStorageKey)
      localStorage.removeItem('socketData')
      return
    }
    const updateRemainingTime = () => {
      setRemainingTime(calculateRemainingTime())
    }

    const interval = setInterval(() => {
      setRemainingTime((prev) => {
        if (prev <= 1) {
          clearInterval(interval)
          localStorage.removeItem(localStorageKey)
          localStorage.removeItem('socketData')
          return 0
        }
        return prev - 1
      })
    }, 1000)

    document.addEventListener('visibilitychange', updateRemainingTime)
    window.addEventListener('focus', updateRemainingTime)

    return () => {
      clearInterval(interval)
      document.removeEventListener('visibilitychange', updateRemainingTime)
      window.removeEventListener('focus', updateRemainingTime)
    }
  }, [remainingTime])

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return { hours, minutes, secs }
  }

  const { hours, minutes, secs } = formatTime(remainingTime || 0)

  const handleContact = () => {
    showIntercom()
  }
  return (
    <MaintenanceWrapper>
      <Grid className='maintenance-wrap'>
        <Box className='maintenance-box'>
          <img src={maintenanceImg} />
          <Typography variant='h3'>We’re Under Maintenance</Typography>
          <Typography variant='body1'>Our site is undergoing Maintenance and we will back soon</Typography>
          <Box className='maintenancetimer'>
            {/* <Box>
              <Box className='maintenanceCountdown'>
                <span className='timer-digit days-digit maintenanceTime'>04</span>
              </Box>
              <Typography variant='span' component='span'>
                Days
              </Typography>
            </Box>
            <span className='counter-divider'>:</span> */}
            <Box>
              <Box className='maintenanceCountdown'>
                <span className='timer-digit hours-digit maintenanceTime'>{hours}</span>
              </Box>
              <Typography variant='span' component='span'>
                Hours
              </Typography>
            </Box>
            <span className='counter-divider'>:</span>
            <Box>
              <Box className='maintenanceCountdown'>
                <span className='timer-digit minutes-digit maintenanceTime'>{minutes}</span>
              </Box>
              <Typography variant='span' component='span'>
                Minutes
              </Typography>
            </Box>
            <span className='counter-divider'>:</span>
            <Box>
              <Box className='maintenanceCountdown'>
                <span className='timer-digit seconds-digit maintenanceTime'>{secs}</span>
              </Box>
              <Typography variant='span' component='span'>
                Seconds
              </Typography>
            </Box>
          </Box>
          <Button className='btn btn-primary' onClick={handleContact}>
            Contact Us
          </Button>
        </Box>
      </Grid>
    </MaintenanceWrapper>
  )
}

export default Maintenance
// const classes = useStyles()
//   let isAllowedUserAccess = localStorage.getItem('allowedUserAccess')
//   dataLayer.push({
//     event: 'geo_block',
//     access_allowed: isAllowedUserAccess
//   })

//   const getEndTime = () => {
//     const storedEndTime = localStorage.getItem('maintenanceEndTime')
//     if (storedEndTime) {
//       return new Date(storedEndTime)
//     }
//     const endTime = new Date()
//     endTime.setHours(endTime.getHours() + 1)
//     localStorage.setItem('maintenanceEndTime', endTime)
//     return endTime
//   }

//   const calculateTimeLeft = (endTime) => {
//     const now = new Date()
//     const difference = endTime - now
//     const timeLeft = Math.max(difference / 1000, 0)
//     return timeLeft
//   }

//   const endTime = getEndTime()
//   const [timeLeft, setTimeLeft] = useState(calculateTimeLeft(endTime))

//   useEffect(() => {
//     if (timeLeft <= 0) return

//     const intervalId = setInterval(() => {
//       const newTimeLeft = calculateTimeLeft(endTime)
//       if (newTimeLeft <= 0) {
//         clearInterval(intervalId)
//         localStorage.removeItem('maintenanceEndTime')
//       }
//       setTimeLeft(newTimeLeft)
//     }, 1000)

//     return () => clearInterval(intervalId)
//   }, [endTime, timeLeft])

//   const formatTime = (seconds) => {
//     const h = Math.floor(seconds / 3600)
//     const m = Math.floor((seconds % 3600) / 60)
//     const s = Math.floor(seconds % 60)
//     return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`
//   }
//   const style = {
//     position: 'absolute',
//     top: '50%',
//     left: '50%',
//     transform: 'translate(-50%, -50%)',
//     width: 400,
//     bgcolor: 'background.paper',
//     border: '2px solid #000',
//     boxShadow: 24,
//     color: '#000',
//     p: 4
//   }
