import { makeStyles } from '@mui/styles'

import { ButtonPrimary, LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  wrapper: {
    maxWidth: '1200px',
    margin: '0 auto',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      marginBottom: '0'
    },
    '&  .becomePartner': {
      ...ButtonPrimary(theme),
      padding: '15px 30px',
      textDecoration: 'none',
      marginTop: '20px',
      display: 'inline-block',
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        padding: '15px 30px'
      },
      [theme.breakpoints.down('sm')]: {
        padding: '8px 16px',
        marginTop: '10px'
      }
    },

    '& .banner': {
      position: 'relative',
      '& img': {
        width: '100%',
        '&.img-1': {
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        },
        '&.img-2': {
          display: 'none',
          [theme.breakpoints.down('sm')]: {
            display: 'block'
          }
        }
      },
      '& .bannerText': {
        '& p': {
          fontSize: '32px',
          fontWeight: 'bold',
          [theme.breakpoints.down('lg')]: {
            fontSize: '19px'
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: '14px'
          }
        },
        position: 'absolute',
        top: '0',
        bottom: '0',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
        left: '45px',
        [theme.breakpoints.down('sm')]: {
          left: '25px'
        }
      }
    },
    '&  .becomePartnerLobbySlider': {
      ...ButtonPrimary(theme),
      textDecoration: 'none',
      marginTop: '20px',
      display: 'inline-block',
      textAlign: 'center'
    },
    '& .lobby-slider-section': {
      height: '100%',
      '& .bannerLobbySlider': {
        position: 'relative',
        borderRadius: theme.spacing(0.625),
        overflow: 'hidden',
        padding: theme.spacing(1),
        // background: `url(${})`,
        backgroundSize: 'cover',
        backgroundPosition: '29% 20%',
        minHeight: theme.spacing(22),
        backgroundRepeat: 'no-repeat',
        [theme.breakpoints.down(1500)]: {
          minHeight: theme.spacing(14),
          backgroundPosition: '24% 100%'
        },
        [theme.breakpoints.down('lg')]: {
          backgroundPosition: '0% 100%'
        },
        [theme.breakpoints.down('md')]: {
          minHeight: theme.spacing(15),
          backgroundPosition: '100% 0%'
        },
        [theme.breakpoints.down('sm')]: {
          minHeight: theme.spacing(10),
          backgroundPosition: '10% 0%'
        },
        // "& > img": {
        //   width: '100%',
        //   height: '100%',
        //   position: "absolute",
        //   left: "0",
        //   top: "0",
        //   objectFit: "cover",
        // },
        // "& .swiper-slider": {
        //   "& img": {

        //   },
        // },
        '& .bannerTextLobbySlider': {
          '& p': {
            fontSize: '15px',
            fontWeight: 'bold',
            marginTop: '20px',
            [theme.breakpoints.down('lg')]: {
              fontSize: '19px'
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: '14px'
            }
          },
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'flex-start',
          [theme.breakpoints.down('sm')]: {
            left: '25px'
          }
        }
      }
    }
  },
  lobbySearchWrap: {
    [theme.breakpoints.down('md')]: {
      display: 'none'
    },
    [theme.breakpoints.down('sm')]: {
      display: 'none'
    },
    [theme.breakpoints.down('xs')]: {
      display: 'none'
    },
    position: 'relative',
    // minWidth:theme.spacing(18.75),
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1.25)
    },
    '& .MuiTextField-root': {
      width: '100%',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        '&::placeholder': {
          color: 'red'
        },
        '&::-ms-input-placeholder': {
          color: 'red'
        },
        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(2.18)
        }
      }
    }
  },
  lobbyProviderWrap: {
    [theme.breakpoints.down('md')]: {
      display: 'none'
    },
    [theme.breakpoints.down('sm')]: {
      display: 'none'
    },
    [theme.breakpoints.down('xs')]: {
      display: 'none'
    },
    '& .MuiSelect-select, & .MuiTextField-root': {
      width: '100%',
      border: `1px solid ${theme.colors.inputBorder}`,
      borderRadius: '0.25rem',
      height: '43px',
      '&:focus': {
        border: `1px solid ${theme.colors.YellowishOrange}`
      }
    },
    '& .uppercaseInput': {
      textTransform: 'uppercase'
    },
    '& .inputSelect': {
      width: '-webkit-fill-available',
      border: `1px solid ${theme.colors.inputBorder}`,
      // borderColor: theme.colors.YellowishOrange,
      color: `${theme.colors.textWhite}!important`,
      padding: '12px 14px',
      backgroundColor: 'transparent',
      borderRadius: '30px',
      minHeight: '56px',
      '&:focus': {
        border: `1px solid ${theme.colors.YellowishOrange}`
      },
      '& option': {
        color: theme.colors.textBlack
      },
      '&:focus-visible': {
        outline: 'none'
      },
      '&:disabled': {
        WebkitTextFillColor: theme.colors.textWhite,
        color: 'rgba(255, 255, 255, 0.6) !important'
      },
      '&::-webkit-inner-spin-button, &::-webkit-outer-spin-button': {
        '-webkit-appearance': 'none',
        margin: 0
      }
    }
  },
  mobLobbySearchWrap: {
    [theme.breakpoints.up('xl')]: {
      display: 'none'
    },
    [theme.breakpoints.down('xl')]: {
      display: 'none'
    },
    [theme.breakpoints.down('lg')]: {
      display: 'none'
    },
    [theme.breakpoints.down('md')]: {
      display: 'block'
    },
    position: 'relative',
    paddingLeft: theme.spacing(1),
    marginTop: theme.spacing(1),
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1.25),
      marginLeft: theme.spacing(1)
    },
    '& .MuiTextField-root': {
      width: '100%',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        width: theme.spacing(17),
        '&::placeholder': {
          color: 'red'
        },
        '&::-ms-input-placeholder': {
          color: 'red'
        },
        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(2.18)
        }
      }
    }
  },
  lobbyRight: {
    ...LobbyRight(theme),

    '& .coin-bundle': {
      [theme.breakpoints.down('md')]: {
        display: 'none'
      }
    },
    '& .referImg': {
      // '& img': {
      //   width: '100%',
      //   "&.img-1": {
      //     [theme.breakpoints.down('sm')]: {
      //       display: 'none',
      //     },
      //   },
      //   "&.img-2": {
      //     display: 'none',
      //     [theme.breakpoints.down('sm')]: {
      //       display: 'block',
      //     },
      //   },
      // },
      '& .banner': {
        position: 'relative',
        '& img': {
          width: '100%',
          borderRadius: theme.spacing(1.25),
          '&.img-1': {
            [theme.breakpoints.down('sm')]: {
              display: 'none'
            }
          },
          '&.img-2': {
            display: 'none',
            [theme.breakpoints.down('sm')]: {
              display: 'block'
            }
          }
        },
        '& .bannerText': {
          '& p': {
            fontSize: '32px',
            fontWeight: 'bold',
            color: '#000',
            [theme.breakpoints.down('lg')]: {
              fontSize: '19px'
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: '14px'
            }
          },
          position: 'absolute',
          top: '0',
          bottom: '0',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'flex-start',
          left: '2.8125rem',
          [theme.breakpoints.down('sm')]: {
            left: '1.5625rem'
          }
        }
      }
    }
  },

  lobbyFilterSection: {
    // [theme.breakpoints.down('md')]: {
    //   display: 'none',
    // },
    // [theme.breakpoints.down('sm')]: {
    //   display: 'none',
    // },
    // [theme.breakpoints.down('xs')]: {
    //   display: 'none',
    // },
    maxWidth: '100%',
    width: '100%',
    display: 'flex',
    whiteSpace: 'nowrap',
    flexWrap: 'nowrap',
    overflowX: 'hidden',
    borderRadius: theme.spacing(2.18),
    // border: `2px solid ${theme.colors.GreenishCyan}`,
    padding: theme.spacing(0),
    '& .lobby-filter-left': {
      overflowX: 'auto',
      padding: '4px',
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(0, 0.25)
      }
    }
  },
  roundedThemeTabs: {
    '& .MuiTabs-scrollButtons': {
      display: 'flex'
    },
    '& .MuiTabs-root': {
      [theme.breakpoints.down('md')]: {
        minHeight: theme.spacing(2.125)
      },
      '& .MuiTabs-scroller': {
        alignItems: 'center',
        display: 'flex',
        marginTop: '1rem',
        '& button': {
          color: theme.colors.textWhite,
          padding: theme.spacing(0.4, 1.2),
          position: 'relative',
          marginRight: theme.spacing(0.5),
          // fontWeight: '500',
          cursor: 'pointer',
          fontSize: theme.spacing(1),
          transition: 'none',
          transform: 'none',
          minWidth: 'auto',
          borderRadius: theme.spacing(4.1875),
          fontWeight: theme.typography.fontWeightExtraBold,
          display: 'flex',
          flexDirection: 'row',
          minHeight: 'auto',

          '&.Mui-selected': {
            background: '#FDB72E',
            color: theme.colors.textBlack
          },
          '&:hover': {
            borderRadius: theme.spacing(4.1875),
            background: '#FDB72E',
            transition: 'none',
            transform: 'none',
            color: theme.colors.textBlack,
            '& span': {
              color: theme.colors.textBlack
            }
          },
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(0.4, 0.25),
            fontSize: theme.spacing(0.875)
          },
          '& img': {
            marginRight: theme.spacing(0.62),
            width: '24px',
            marginBottom: 0,
            [theme.breakpoints.down('md')]: {
              width: '18px'
            }
          },
          '&.active': {
            borderRadius: theme.spacing(4.1875),
            background: '#D6A300',
            fontWeight: theme.typography.fontWeightExtraBold,
            '& span': {
              color: theme.colors.textBlack
            }
          }
        }
      },
      '& .MuiTabs-indicator': {
        display: 'none'
      }
    }
  },
  lobbyFilterMobileSection: {
    [theme.breakpoints.up('xl')]: {
      display: 'none'
    },
    [theme.breakpoints.down('xl')]: {
      display: 'none'
    },
    [theme.breakpoints.down('lg')]: {
      display: 'none'
    },
    [theme.breakpoints.down('md')]: {
      display: 'block'
    }
  },

  '& .swiper-wrapper': {
    display: 'none',
    '& .swiper-slide': {
      '& img': {
        width: '100%'
      }
    }
  },

  innerHeading: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(0.97),
    marginTop: theme.spacing(1),
    alignItems: 'center',
    '& .heading-left': {
      display: 'flex',
      gap: theme.spacing(1),
      '& p': {
        color: theme.colors.textWhite,
        fontSize: theme.spacing(1.5625),
        fontWeight: 400
      }
    }
  },

  innerBannerSection: {
    marginBottom: theme.spacing(1.5),
    '& .inner-banner-card': {
      position: 'relative',
      width: '100%',
      borderRadius: theme.spacing(0.625),
      paddingBottom: '24%',
      overflow: 'hidden',
      '&:before': {
        position: 'absolute',
        content: "''",
        width: '100%',
        height: '100%',
        top: '0',
        left: '0',
        background: theme.colors.bannerOverlay,
        zIndex: '1',
        display: 'none',
        [theme.breakpoints.down('md')]: {
          display: 'block'
        }
      },
      [theme.breakpoints.down('md')]: {
        paddingBottom: '30%'
      },
      [theme.breakpoints.down('sm')]: {
        paddingBottom: '40%'
      },
      '& img': {
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        position: 'absolute'
      },
      '& .inner-banner-content': {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '100%',
        paddingLeft: theme.spacing(3),
        [theme.breakpoints.down('lg')]: {
          paddingLeft: theme.spacing(1)
        },
        zIndex: '1',
        '& .MuiTypography-h3': {
          color: theme.colors.white,
          fontSize: theme.spacing(4),
          fontWeight: theme.typography.fontWeightBold,
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(1.5)
          },
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(2)
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1.2)
          }
        },
        '& .MuiTypography-body1': {
          color: theme.colors.white,
          fontSize: theme.spacing(1.4),
          fontWeight: theme.typography.fontWeightMedium,
          maxWidth: '700px',
          marginBottom: theme.spacing(1),
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(0.875)
          },
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(0.75)
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.6)
          }
        }
      }
    }
  },

  subcategoryListWrap: {
    display: 'grid',
    paddingTop: '10px',
    alignItems: 'stretch',
    gridTemplateColumns: 'repeat(auto-fill, minmax(13%, 1fr))',
    gap: '12px 12px',

    [theme.breakpoints.down('lg')]: {
      gridTemplateColumns: 'repeat(auto-fill, minmax(16%, 1fr))',
      marginBottom: '45px'
    },
    [theme.breakpoints.down('sm')]: {
      gridTemplateColumns: 'repeat(auto-fill, minmax(30%, 1fr))'
    },
    [theme.breakpoints.down('md')]: {
      gridTemplateColumns: 'repeat(auto-fill, minmax(23%, 1fr))'
    },
    '& .casino-card': {
      position: 'relative',
      transition: 'all 200ms ease-in-out',
      lineHeight: '0',
      // maxWidth:'119px',
      // maxHeight: '168px',
      '& .fav-icon': {
        width: '20px',
        height: '20px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: '8',
        '& img': {
          width: '20px%',
          height: '20px',
          objectFit: 'contain',
          objectPosition: 'center'
        },
        '&:hover': {
          // backgroundColor: theme.colors.textWhite,
          cursor: 'pointer'
        },
        '& .tournamentLogo': {
          position: 'relative',
          right: '0',
          top: '0'
        }
      },
      '& .prgamatic-jackpot-amount-wrapper': {
        position: 'absolute',
        top: '12px',
        left: '47%',
        display: 'flex',
        justifyContent: 'center',
        gap: '4px',
        alignItems: 'center',
        background: '#000000B2',
        borderRadius: '17px',
        whiteSpace: 'nowrap',
        transform: 'translate(-50%, 0)',
        padding: '1px 5px'
      },
      '& .casinoGame-img': {
        width: '100%',
        aspectRatio: '2/3',
        borderRadius: '8px',
        height: '100%',
        '&:hover': {
          backgroundColor: theme.colors.textWhite,
          cursor: 'pointer'
        }
      },
      '& .casino-img': {
        width: '100%',
        aspectRatio: '1'
      },
      '&:hover': {
        transform: 'translateY(-0.25rem)',
        '& .casino-overlay': {
          display: 'flex',
          opacity: '1',
          transition: 'all 300ms ease-in-out'
        }
      },
      '& .casino-overlay': {
        position: 'absolute',
        opacity: '0',
        display: 'flex',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        margin: '0 auto',
        inset: '0',
        flexDirection: 'column',
        background: 'linear-gradient(180deg, rgba(255,84,37,0.9) 0%, rgba(251,162,83,0.9) 100%)',
        cursor: 'pointer',
        transition: 'all 200ms ease-in-out',
        borderRadius: '8px',
        color: theme.colors.textWhite,
        '& a': {
          color: theme.colors.textWhite,
          textDecoration: 'none'
        },
        '& h6': {
          color: theme.colors.textWhite
          // wordBreak:'break-all'
        }
      },
      '& .tournamentLogo': {
        position: 'absolute',
        left: '2px',
        top: '5px',
        width: '30px',
        height: '30px'
      }
    }
  },

  loadMore: {
    width: '100%',
    textAlign: 'center',
    margin: theme.spacing(2.5, 0, 1, 0),
    '& button': {
      ...ButtonPrimary(theme),
      '&:hover': {
        ...ButtonPrimary(theme)
      }
    }
  },

  '& .no-data-content': {
    width: '100%',
    textAlign: 'center'
  },

  playImg: {
    width: '60px',
    margin: '20px 0',
    height: 'auto',
    [theme.breakpoints.down('xl')]: {
      width: '40px',
      margin: '5px 0'
    }
  },

  referBanner: {
    // padding: "1rem",
    // borderRadius: "1rem",
    // backgroundColor: theme.colors.Pastel,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    '& .applyCode': {
      display: 'flex',
      gap: '15px',
      '& input': {
        color: theme.colors.textWhite,
        width: '100%',
        border: `2px solid ${theme.colors.GreenishCyan}`,
        padding: '12px 14px',
        backgroundColor: 'transparent',
        paddingLeft: '15px',
        borderRadius: '50px',
        '&:focus, &:focus-visible': {
          outline: 'none',
          border: `2px solid ${theme.colors.YellowishOrange}`
        }
      },
      '& button': {
        ...ButtonPrimary(theme)
      }
    },
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'column !important'
    },
    '& .heading': {
      color: theme.colors.Yellowish,
      fontWeight: 'bold',
      fontSize: theme.spacing(3.5),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(2)
      }
    },
    '& .text': {
      color: theme.colors.textWhite,
      fontWeight: 'bold',
      fontSize: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      }
    },
    '& .referImg': {
      '& img': {
        height: '300px',
        objectFit: 'cover',
        objectPosition: 'top',
        width: '100%',
        '&.img-1': {
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        },
        '&.img-2': {
          display: 'none',
          [theme.breakpoints.down('sm')]: {
            display: 'block'
          }
        }
      }
    }
  },

  tablePagination: {
    '& ul': {
      justifyContent: 'center',
      '& li': {
        '& button': {
          color: theme.colors.textWhite
        },
        '& .MuiPaginationItem-root.Mui-selected': {
          backgroundColor: theme.colors.YellowishOrange,
          color: theme.colors.textBlack
        }
      }
    }
  },

  referSection: {
    '& .middleText': {
      fontWeight: 'bold',
      fontSize: theme.spacing(2),
      color: theme.colors.textWhite,
      marginBottom: theme.spacing(2),
      '& span': {
        fontWeight: 'bold',
        fontSize: theme.spacing(2),
        '&.sc': {
          color: theme.colors.Yellowish
        },
        '&.gc': {
          color: theme.colors.Yellowish
        }
      }
    },
    '& .listItem': {
      padding: '0',
      margin: '0',
      '& li': {
        fontSize: theme.spacing(1.5),
        padding: theme.spacing(0, 0, 1, 0),
        fontWeight: 'bold'
      }
    },
    '& .text1, .text2': {
      fontSize: theme.spacing(1.5),
      marginBottom: theme.spacing(1)
    },

    '& .referInput': {
      display: 'flex',
      alignItems: 'center',
      width: '100%',
      '& .referSelect': {
        position: 'relative',
        width: '100%',
        display: 'flex',
        '& select': {
          position: 'absolute',
          left: '20px',
          border: '0',
          color: theme.colors.textWhite,
          backgroundColor: 'transparent',
          height: '100%',
          borderRadius: '0',
          '& option': {
            color: theme.colors.textBlack,
            borderRadius: '0'
          },
          '&:focus, &:focus-visible': {
            outline: 'none'
          }
        },
        '& input': {
          color: theme.colors.textWhite,
          width: '100%',
          border: `2px solid ${theme.colors.GreenishCyan}`,
          padding: '12px 0px 14px 14px',
          backgroundColor: 'transparent',
          [theme.breakpoints.down('sm')]: {
            paddingRight: '80px'
          },
          borderRadius: '50px',
          '&:focus, &:focus-visible': {
            outline: 'none',
            border: `2px solid ${theme.colors.YellowishOrange}`
          }
        }
      }
    },

    '& .createBonusBtn': {
      ...ButtonPrimary(theme),
      position: 'absolute',
      right: '0px',
      top: '2px',
      [theme.breakpoints.down('lg')]: {
        minWidth: 'auto',
        whiteSpace: 'nowrap',
        padding: '7px 16px',
        marginLeft: theme.spacing(1.4375)
      },

      '&:hover': {
        ...ButtonPrimary(theme)
      }
    },

    '& .btn-gradient': {
      ...ButtonPrimary(theme),
      marginLeft: '15px',
      [theme.breakpoints.down('lg')]: {
        minWidth: 'auto',
        whiteSpace: 'nowrap',
        padding: '7px 16px',
        marginLeft: '15px'
      },
      '&:hover': {
        ...ButtonPrimary(theme),
        [theme.breakpoints.down('lg')]: {
          minWidth: 'auto',
          whiteSpace: 'nowrap',
          padding: '7px 16px'
        }
      }
    },

    '& .howToWork': {
      display: 'flex',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      textAlign: 'center',
      gap: '30px',
      margin: '50px 0',
      [theme.breakpoints.down('md')]: {
        flexDirection: 'column'
      },
      '& .grid1': {
        width: 'calc(100% / 3 - 30px)',
        '& img': {
          width: '100px'
        },
        [theme.breakpoints.down('md')]: {
          width: '100%'
        },
        '& h6': {
          fontWeight: 'bold',
          fontSize: theme.spacing(1.5)
        },
        '& p': {
          fontSize: theme.spacing(1.3),
          '& span': {
            color: theme.colors.Yellowish,
            fontWeight: 'bold'
          }
        }
      }
    },

    '& .friendStatics': {
      padding: '2rem 1rem',
      borderRadius: '1rem',
      backgroundColor: theme.colors.Pastel,
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        flexDirection: 'column'
      },
      '& .sectionGroup': {
        [theme.breakpoints.down('sm')]: {
          flexDirection: 'column'
        },
        gap: '30px',
        display: 'flex',
        '& .section1': {
          display: 'flex',
          gap: '15px',
          [theme.breakpoints.down('sm')]: {
            flexDirection: 'column',
            alignItems: 'center'
          }
        },
        '& .section2': {
          [theme.breakpoints.down('sm')]: {
            textAlign: 'center'
          }
        }
      }
    },

    '& .friendTable': {
      '& table': {
        [theme.breakpoints.down('sm')]: {
          // display: 'none',
        },
        '& thead': {
          '& tr': {
            '& th': {
              color: theme.colors.osloGrey,
              fontSize: '1rem',
              fontStyle: 'normal',
              fontWeight: 700,
              lineHeight: 'normal',
              borderBottom: 'none',
              textAlign: 'center',
              '&:nth-child(2)': {
                [theme.breakpoints.down('md')]: {
                  // display: "none",
                },
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              },
              '&:nth-child(3)': {
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              },
              '&:nth-child(4)': {
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              }
            }
          }
        },
        '& tbody': {
          '& tr': {
            '&:nth-of-type(odd)': {
              background: `${theme.colors.GreenishCyan}`
            },
            '& td': {
              color: theme.colors.textWhite,
              fontSize: '1rem',
              fontStyle: 'normal',
              fontWeight: 400,
              lineHeight: 'normal',
              borderBottom: 'none',
              textAlign: 'center',
              '&:first-child': {
                borderRadius: theme.spacing(3.5, 0, 0, 3.5)
              },
              '&:last-child': {
                borderRadius: theme.spacing(0, 3.5, 3.5, 0)
              },
              '&:nth-child(2)': {
                [theme.breakpoints.down('md')]: {
                  // display: "none",
                },

                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              },
              '&:nth-child(3)': {
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              },
              '&:nth-child(4)': {
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              }
            }
          }
        }
      }
    },
    '& .divider': {
      backgroundColor: theme.colors.textWhite,
      margin: '0',
      width: '1px',
      [theme.breakpoints.down('sm')]: {
        width: '100%'
      }
    }
  },

  personalSection: {
    '& .create-bonus-section': {
      '& input, & select': {
        display: 'block',
        width: '-webkit-fill-available',
        color: theme.colors.textWhite,
        border: `2px solid ${theme.colors.GreenishCyan}`,
        padding: '12px 14px',
        backgroundColor: 'transparent',
        paddingLeft: '15px',
        borderRadius: '50px',
        marginTop: '10px',
        '&:focus, &:focus-visible': {
          outline: 'none',
          border: `2px solid ${theme.colors.YellowishOrange}`
        }
      },
      '& .input-error': {
        color: theme.colors.error,
        fontSize: `${theme.spacing(0.8)}!important`,
        margin: '0 !important',
        lineHeight: 'normal !important',
        minHeight: '16px',
        fontWeight: '600'
      },
      '& select': {
        display: 'block',
        width: '-webkit-fill-available',
        color: theme.colors.textWhite,
        border: `2px solid ${theme.colors.GreenishCyan}`,
        padding: '12px 14px',
        backgroundColor: 'transparent',
        paddingLeft: '15px',
        borderRadius: '50px',
        '&:focus, &:focus-visible': {
          outline: 'none',
          border: `2px solid ${theme.colors.YellowishOrange}`
        },
        '& option': {
          color: theme.colors.textBlack
        }
      },
      '& button': {
        color: theme.colors.textBlack,
        fontSize: '16px',
        background: theme.colors.YellowishOrange,
        boxShadow: '0px 0px 12px rgba(203, 184, 186, 0.20)',
        fontWeight: 'bold',
        borderRadius: '7.8rem',
        minWidth: '120px',
        margin: theme.spacing(0.8, 0, 0)
      }
    },
    '& .friendTable': {
      '& .friendBonusTable': {
        overflowX: 'auto'
      },

      '& table': {
        [theme.breakpoints.down('sm')]: {
          // display: 'none',
        },
        '& thead': {
          '& tr': {
            '& th': {
              color: theme.colors.osloGrey,
              fontSize: '1rem',
              fontStyle: 'normal',
              fontWeight: 700,
              lineHeight: 'normal',
              borderBottom: 'none',
              textAlign: 'center',
              '&:nth-child(2)': {
                [theme.breakpoints.down('md')]: {
                  // display: "none",
                },
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              },
              '&:nth-child(3)': {
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              },
              '&:nth-child(4)': {
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              }
            }
          }
        },
        '& tbody': {
          '& tr': {
            '&:nth-of-type(odd)': {
              background: `${theme.colors.GreenishCyan}`
            },
            '& td': {
              color: theme.colors.textWhite,
              fontSize: '1rem',
              fontStyle: 'normal',
              fontWeight: 400,
              lineHeight: 'normal',
              borderBottom: 'none',
              textAlign: 'center',
              '&:first-child': {
                borderRadius: theme.spacing(3.5, 0, 0, 3.5)
              },
              '&:last-child': {
                borderRadius: theme.spacing(0, 3.5, 3.5, 0)
              },
              '&:nth-child(2)': {
                [theme.breakpoints.down('md')]: {
                  // display: "none",
                },

                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              },
              '&:nth-child(3)': {
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              },
              '&:nth-child(4)': {
                [theme.breakpoints.down('sm')]: {
                  // display: "none",
                }
              }
            }
          }
        }
      }
    },
    '& .MuiTypography-bonusSection': {
      display: 'block',
      fontSize: theme.spacing(2),
      marginBottom: theme.spacing(1),
      fontWeight: 'bold',
      textAlign: 'left'
    },
    '& .referral-section-text': {
      display: 'flex',
      justifyContent: 'space-between',
      margin: theme.spacing(4.56, 0, 2.56, 0),

      '& .referral-section': {
        backgroundColor: theme.colors.textBlack,
        padding: theme.spacing(2.56),
        width: '35%',

        [theme.breakpoints.down(1023)]: {
          width: '100%'
        }
      },
      '& .MuiTypography-heading1': {
        display: 'block',
        fontSize: theme.spacing(2),
        marginBottom: theme.spacing(1),
        fontWeight: 'bold',
        textAlign: 'center'
      },
      '& input': {
        display: 'block',
        width: '-webkit-fill-available',
        color: theme.colors.textWhite,
        border: `2px solid ${theme.colors.GreenishCyan}`,
        padding: '12px 14px',
        backgroundColor: 'transparent',
        paddingLeft: '15px',
        borderRadius: '50px',
        '&:focus, &:focus-visible': {
          outline: 'none',
          border: `2px solid ${theme.colors.YellowishOrange}`
        }
      },
      '& button': {
        color: theme.colors.textBlack,
        fontSize: '16px',
        background: theme.colors.YellowishOrange,
        boxShadow: '0px 0px 12px rgba(203, 184, 186, 0.20)',
        fontWeight: 'bold',
        borderRadius: '7.8rem',
        width: '100%',
        margin: theme.spacing(1.5, 0)
      },

      [theme.breakpoints.down(1023)]: {
        flexDirection: 'column',
        margin: theme.spacing(2.56, 0, 2.56, 0)
      }
    },

    '& .textRightSection': {
      width: '55%',
      '& ul': {
        '& li': {
          gap: '20px'
        }
      },

      [theme.breakpoints.down(1023)]: {
        width: '100%',
        marginTop: '1rem'
      }
    },

    '& .friendStatics': {
      padding: '2rem 1rem',
      borderRadius: '1rem',
      backgroundColor: theme.colors.Pastel,
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        flexDirection: 'column'
      },
      '& .sectionGroup': {
        [theme.breakpoints.down('sm')]: {
          flexDirection: 'column'
        },
        gap: '30px',
        display: 'flex',
        '& .section1': {
          display: 'flex',
          gap: '15px',
          [theme.breakpoints.down('sm')]: {
            flexDirection: 'column',
            alignItems: 'center'
          }
        },
        '& .section2': {
          [theme.breakpoints.down('sm')]: {
            textAlign: 'center'
          }
        }
      }
    },

    '& .MuiTypography-text1 ': {
      color: theme.colors.YellowishOrange,
      fontSize: theme.spacing(1.5625),
      fontWeight: 600
    },
    '& .MuiTypography-text2': {
      color: theme.colors.textWhite,
      fontSize: theme.spacing(1.25),
      fontWeight: 400
    },
    '& .MuiTypography-heading2': {
      color: theme.colors.textWhite,
      fontSize: theme.spacing(2),
      fontWeight: 700,
      display: 'block'
    },
    '& .MuiTypography-heading3': {
      color: theme.colors.YellowishOrange,
      fontSize: theme.spacing(2),
      fontWeight: 700,
      display: 'block',
      marginBottom: theme.spacing(2)
    },
    '& .MuiTypography-text': {
      color: theme.colors.textWhite,
      fontSize: theme.spacing(1.25),
      fontWeight: 400,
      marginBottom: theme.spacing(2),
      display: 'block'
    }
  },

  bonusBoxImg: {
    '& img': {
      [theme.breakpoints.down(1300)]: {
        width: '100%'
      },

      [theme.breakpoints.down(1023)]: {
        width: 'auto'
      }
    },
    [theme.breakpoints.down(1023)]: {
      textAlign: 'center'
    }
  },

  bannerCoinBundle: {
    background: theme.colors.coinBundle,
    padding: theme.spacing(1.25, 1.25),
    display: 'block',
    borderRadius: theme.spacing(0.625),
    minHeight: '195px',
    height: '100%',
    justifyContent: 'space-between',
    [theme.breakpoints.down('md')]: {
      display: 'none'
    },
    // margin: theme.spacing(4),

    '& .MuiTypography-h4': {
      fontSize: theme.spacing(1),
      fontWeight: 600,
      paddingBottom: theme.spacing(0.625)
    },

    '& .bundle-button': {
      display: 'grid',
      gap: theme.spacing(0.3125),
      alignItems: 'center',
      gridTemplateColumns: 'repeat(3, minmax(auto, 1fr))',
      '& .MuiButtonBase-root': {
        fontSize: theme.spacing(0.875),
        background: theme.colors.bundleBtn,
        padding: theme.spacing(0.625, 0.25),
        color: theme.colors.textWhite,
        borderRadius: theme.spacing(0.625),
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',

        '&.active': {
          border: `2px solid ${theme.colors.Pastel}`,
          fontWeight: '600',
          '& img': {
            marginRight: theme.spacing(0.375),
            width: '1rem'
          }
        }
      }
    },

    '& .coin-box': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: theme.spacing(1),

      '& .bundle-coins': {
        '& .MuiBox-root': {
          fontWeight: '500',
          '& img': {
            width: '1rem'
          }
        }
      },

      '& button': {
        borderRadius: theme.spacing(7.8),
        padding: theme.spacing(0.375, 2),
        color: theme.colors.textBlack,
        fontWeight: theme.typography.fontWeightExtraBold,
        fontSize: theme.spacing(1),
        background: theme.colors.YellowishOrange,
        gap: theme.spacing(0.4),
        textTransform: 'uppercase',
        minWidth: '115px',
        [theme.breakpoints.down('md')]: {
          minWidth: '75px',
          padding: theme.spacing(0.375, 0)
        },
        '& img': {
          width: '0'
        },
        '&:hover': {
          '& img': {
            width: 'auto',
            marginTop: '-5px'
          }
        }
      }
    }
  },
  timerWrapper: {
    position: 'fixed',
    right: '20px',
    bottom: '40px',
    zIndex: 3,
    cursor: 'pointer',
    [theme.breakpoints.down('md')]: {
      top: theme.spacing(16.875),
      bottom: 'auto'
    },
    '& .timer-card': {
      background: '#FDB72E',
      padding: theme.spacing(0.313, 1),
      borderRadius: '30px',
      color: theme.colors.textBlack,
      fontWeight: theme.typography.fontWeightBold,
      textAlign: 'center',
      fontSize: '1.5rem',
      [theme.breakpoints.down('md')]: {
        fontSize: '0.875rem',
        padding: theme.spacing(0.2, 0.313)
      }
    },
    '& .timer-bottom-box': {
      height: theme.spacing(5),
      width: theme.spacing(5),
      background: theme.colors.lightBlack,
      borderRadius: '100%',
      margin: '0 auto',
      [theme.breakpoints.down('md')]: {
        width: theme.spacing(2.5),
        height: theme.spacing(2.5)
      }
    }
  },
  subCategorySelect: {
    width: '100%',
    maxWidth: '400px' /* Optional: Limit the maximum width */,
    minWidth: '200px' /* Optional: Set a minimum width */,
    boxSizing: 'border-box',
    '& .reactInnerCoinSelect__control': {
      borderRadius: theme.spacing(3.125),
      backgroundColor: 'transparent',
      border: `2px solid ${theme.colors.GreenishCyan}`,
      marginTop: theme.spacing(0.625),
      cursor: 'pointer',

      '& .reactInnerCoinSelect__value-container': {
        padding: theme.spacing(0.3125, 0.5),

        '& .reactInnerCoinSelect__single-value': {
          color: theme.colors.textWhite
        }
      },

      '& .reactInnerCoinSelect__indicator-separator': {
        display: 'none'
      },

      '&.reactInnerCoinSelect__control--is-focused': {
        boxShadow: 'none',
        outline: 'none'
      },

      '&:hover': {
        boxShadow: 'none',
        outline: 'none',
        border: `2px solid ${theme.colors.GreenishCyan}`
      }
    },

    '& .reactInnerCoinSelect__menu': {
      background: theme.colors.GreenishCyan,
      zIndex: '1000',
      '& .reactInnerCoinSelect__option': {
        cursor: 'pointer',
        fontWeight: '500',
        zIndex: '1000',

        '&.reactInnerCoinSelect__option--is-focused': {
          background: theme.colors.YellowishOrange,
          color: theme.colors.textBlack
        },

        '&.reactInnerCoinSelect__option--is-selected': {
          background: 'transparent',
          color: theme.colors.textWhite,

          '&:hover': {
            background: theme.colors.YellowishOrange,
            color: theme.colors.textBlack
          }
        }
      }
    }
  },
  providerSelect: {
    width: '100%',
    maxWidth: '400px' /* Optional: Limit the maximum width */,
    minWidth: '200px' /* Optional: Set a minimum width */,
    boxSizing: 'border-box',
    '& .reactInnerCoinSelect__control': {
      borderRadius: theme.spacing(3.125),
      backgroundColor: 'transparent',
      border: `2px solid ${theme.colors.GreenishCyan}`,
      marginTop: theme.spacing(0.625),
      cursor: 'pointer',
      [theme.breakpoints.up('md')]: {
        marginTop: theme.spacing(0),
        padding: '4.5px 0px 8.5px 5px'
      },

      '& .reactInnerCoinSelect__value-container': {
        padding: theme.spacing(0.3125, 0.5),

        '& .reactInnerCoinSelect__single-value': {
          color: theme.colors.textWhite
        }
      },

      '& .reactInnerCoinSelect__indicator-separator': {
        display: 'none'
      },

      '&.reactInnerCoinSelect__control--is-focused': {
        boxShadow: 'none',
        outline: 'none'
      },

      '&:hover': {
        boxShadow: 'none',
        outline: 'none',
        border: `2px solid ${theme.colors.GreenishCyan}`
      }
    },

    '& .reactInnerCoinSelect__menu': {
      background: theme.colors.GreenishCyan,
      zIndex: '1000',
      '& .reactInnerCoinSelect__option': {
        cursor: 'pointer',
        fontWeight: '500',
        zIndex: '1000',

        '&.reactInnerCoinSelect__option--is-focused': {
          background: theme.colors.YellowishOrange,
          color: theme.colors.textBlack
        },

        '&.reactInnerCoinSelect__option--is-selected': {
          background: 'transparent',
          color: theme.colors.textWhite,

          '&:hover': {
            background: theme.colors.YellowishOrange,
            color: theme.colors.textBlack
          }
        }
      }
    }
  },
  lobbyFilterWrap: {
    // [theme.breakpoints.up('md')]: {
    //   display: 'flex',
    // },
    // [theme.breakpoints.up('sm')]: {
    //   display: 'flex',
    // },
    // [theme.breakpoints.up('xs')]: {
    //   display: 'flex',
    // },
    display: 'flex',
    justifyContent: 'space-between',
    gap: theme.spacing(0.625)
    // [theme.breakpoints.down('md')]: {
    //   flexDirection: "column !important",
    // },
  },
  mobilelobbyFilterWrap: {
    [theme.breakpoints.up('md')]: {
      display: 'none'
    },
    display: 'flex',
    justifyContent: 'space-between',
    gap: theme.spacing(0.625),
    [theme.breakpoints.down('md')]: {
      flexDirection: 'column !important'
    }
  },
  lobbySearchMobileWrap: {
    // marginTop: theme.spacing(1),
    // marginLeft : theme.spacing(5),
    // position: "fixed",
    marginTop: theme.spacing(0.625),
    padding: '4.5px 0px 8.5px 5px',
    [theme.breakpoints.up('md')]: {
      marginTop: theme.spacing(0),
      padding: '4.5px 0px 8.5px 5px'
    }
  }
}))
