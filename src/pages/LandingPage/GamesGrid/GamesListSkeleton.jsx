import React from 'react'
import { Box, Grid } from '@mui/material'
import useStyles from '../../Lobby/Lobby.styles'

const GamesListSkeleton = ({ count = 12 }) => {
  const classes = useStyles()

  // Create skeleton game cards
  const skeletonCards = Array.from({ length: count }, (_, index) => (
    <Grid key={`skeleton-game-list-${index}`} className='custom-col-2'>
      <Box
        sx={{
          position: 'relative',
          borderRadius: '8px',
          overflow: 'hidden',
          backgroundColor: '#333234',
          aspectRatio: '3/4', // Game card aspect ratio
          minHeight: '200px',
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          transition: 'transform 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.02)'
          }
        }}
      >
        {/* Game image skeleton */}
        <Box
          sx={{
            flex: 1,
            backgroundColor: '#444',
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent)',
              animation: 'shimmer 1.5s infinite'
            }
          }}
        />
        
        {/* Game title skeleton */}
        <Box
          sx={{
            padding: '8px',
            backgroundColor: '#333234'
          }}
        >
          <Box
            sx={{
              width: `${60 + (index % 3) * 20}%`, // Varying widths for more realistic look
              height: '12px',
              backgroundColor: '#555',
              borderRadius: '6px',
              margin: '0 auto'
            }}
          />
        </Box>

        {/* Favorite icon skeleton */}
        <Box
          sx={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            width: '24px',
            height: '24px',
            backgroundColor: '#555',
            borderRadius: '50%',
            opacity: 0.7
          }}
        />

        {/* Tournament badge skeleton (for some cards) */}
        {index % 4 === 0 && (
          <Box
            sx={{
              position: 'absolute',
              top: '8px',
              left: '8px',
              width: '20px',
              height: '20px',
              backgroundColor: '#666',
              borderRadius: '4px'
            }}
          />
        )}

        {/* Jackpot amount skeleton (for some cards) */}
        {index % 5 === 0 && (
          <Box
            sx={{
              position: 'absolute',
              bottom: '40px',
              left: '8px',
              right: '8px',
              height: '16px',
              backgroundColor: '#555',
              borderRadius: '8px',
              opacity: 0.8
            }}
          />
        )}
      </Box>
    </Grid>
  ))

  return (
    <>
      <style>
        {`
          @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
          }
        `}
      </style>
      <Grid className='games-grid'>
        <Grid className={classes.subcategoryListWrap}>
          {skeletonCards}
        </Grid>
      </Grid>
    </>
  )
}

export default GamesListSkeleton
