import React, { useState, useEffect } from 'react'
import { Box, Grid, Typography, useTheme } from '@mui/material'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import 'swiper/css/navigation'

import UserIcon from '../../components/ui-kit/icons/svg/user-icon.svg'
import TrophyIcon from '../../components/ui-kit/icons/svg/trophy-logo.svg'
import GCIcon from '../../../src/components/ui-kit/icons/utils/card-coin2.webp'
import DefaultImg from '../../../src/components/ui-kit/icons/utils/casinoGames.webp'
import ScIcon from '../../components/ui-kit/icons/utils/usd-cash.webp'
import { LatestWinnerWrapper } from './Latestwinners.styles'
import { useUserStore } from '../../store/useUserSlice'
import { liveWinnerSocket } from '../../utils/socket'
import useLiveWinners from './hooks/useLiveWiners'
import LiveWinnersSkeleton from './LiveWinnersSkeleton'

const LiveWinners = (props) => {
  const theme = useTheme()
  const liveWinnerData = useLiveWinners()

  const liveWinnerSocketConnection = useUserStore((state) => state.liveWinnerSocketConnection)

  const [winnerList, setWinnerList] = useState(liveWinnerData?.data?.data?.data || [])
  const [isLoading, setIsLoading] = useState(true)

  const onGetLiveWinners = (winnerData) => {
    setWinnerList((winnerList) => {
      const updatedWinners = [winnerData?.data, ...winnerList.slice(0, winnerList.length - 1)]
      return updatedWinners
    })
  }

  // Preload critical icons for better performance
  useEffect(() => {
    const iconsToPreload = [UserIcon, TrophyIcon, GCIcon, ScIcon]
    iconsToPreload.forEach(iconSrc => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = iconSrc
      document.head.appendChild(link)
    })
  }, [])
  useEffect(() => {
    if (liveWinnerSocketConnection) {
      liveWinnerSocket.on('LIVE_GAME_WINNERS', (data) => {
        onGetLiveWinners(data)
      })
    }
    return () => {
      liveWinnerSocket.off('LIVE_GAME_WINNERS', () => {})
    }
  }, [liveWinnerSocketConnection])

  useEffect(() => {
    if (liveWinnerData?.data?.data?.data) {
      setWinnerList(liveWinnerData.data.data.data)
      setIsLoading(false)
    }
  }, [liveWinnerData?.data?.data?.data])

  // Show skeleton while loading
  if (isLoading || !winnerList || winnerList.length === 0) {
    return <LiveWinnersSkeleton />
  }
  return (
    <section key={`sliderGrid-${props.subCategory?.name?.EN}-${props.index}`}>
      <LatestWinnerWrapper theme={theme}>
        <Grid className='gameHeading'>
          <Grid className='heading'>
            <figure>
              <img
                src={TrophyIcon}
                alt="Trophy icon"
                width='28px'
                height='28px'
                loading="eager"
                decoding="async"
              />
            </figure>
            <Typography>Live Winners</Typography>
          </Grid>
        </Grid>
        <Box className='winners-wrap'>
          {winnerList &&
            winnerList?.map((winner, index) => (
              <Box key={`winner_${index}`} className='outer-box'>
                <Box className='live-winner-box'>
                  <figure>
                    <img
                      src={winner.gameImage || DefaultImg}
                      alt={`${winner.username}'s winning game`}
                      className='casinoGame-img'
                      width='100%'
                      height='71px'
                      loading={index < 3 ? 'eager' : 'lazy'} // Load first 3 eagerly
                      decoding="async"
                      style={{
                        aspectRatio: '52/71',
                        objectFit: 'cover',
                        display: 'block'
                      }}
                    />
                  </figure>

                  <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
                    <Box className='winnerName' display='flex' justifyContent='center' alignItems='center'>
                      <figure>
                        <img
                          src={UserIcon}
                          alt='User icon'
                          width="20px"
                          height="20px"
                          loading="eager"
                          decoding="async"
                        />
                      </figure>
                      <Typography component='span'>{winner.username}</Typography>
                    </Box>

                    <Typography
                      className={`winnerAmount ${winner.isScActive ? 'green-text' : 'yellow-text'}`}
                      display='flex'
                      justifyContent='center'
                      alignItems='center'
                    >
                      <figure>
                        <img
                          src={winner.isScActive ? ScIcon : GCIcon}
                          alt={winner.isScActive ? 'SC icon' : 'GC icon'}
                          width="20px"
                          height="20px"
                          loading="eager"
                          decoding="async"
                        />
                      </figure>
                      <span>
                        {' '}
                        {winner.winAmount} {winner.isScActive ? 'SC' : 'GC'}
                      </span>
                    </Typography>
                  </Grid>
                </Box>
              </Box>
            ))}
        </Box>
      </LatestWinnerWrapper>
    </section>
  )
}

export default LiveWinners
