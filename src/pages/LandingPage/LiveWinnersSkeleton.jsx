import React from 'react'
import { Box, Grid, Typography, useTheme } from '@mui/material'
import { LatestWinnerWrapper } from './Latestwinners.styles'
import TrophyIcon from '../../components/ui-kit/icons/svg/trophy-logo.svg'

const LiveWinnersSkeleton = () => {
  const theme = useTheme()

  // Create skeleton items based on responsive design
  const skeletonItems = Array.from({ length: 5 }, (_, index) => (
    <Box key={`skeleton_${index}`} className='outer-box'>
      <Box className='live-winner-box'>
        {/* Game image skeleton */}
        <Box
          sx={{
            width: '52px',
            height: '71px',
            backgroundColor: '#333234',
            borderRadius: '0.5rem',
            position: 'relative',
            overflow: 'hidden',
            [theme.breakpoints.down('md')]: {
              width: '38px',
              height: '56px'
            }
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: `
                linear-gradient(
                  110deg,
                  transparent 0%,
                  transparent 40%,
                  rgba(255, 255, 255, 0.08) 50%,
                  rgba(255, 255, 255, 0.12) 55%,
                  rgba(255, 255, 255, 0.08) 60%,
                  transparent 70%,
                  transparent 100%
                )
              `,
              backgroundSize: '200% 100%',
              animation: 'shimmer 2s ease-in-out infinite',
              '@keyframes shimmer': {
                '0%': {
                  backgroundPosition: '-200% 0'
                },
                '100%': {
                  backgroundPosition: '200% 0'
                }
              }
            }}
          />
        </Box>

        <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
          {/* Username skeleton */}
          <Box className='winnerName' display='flex' justifyContent='center' alignItems='center'>
            <Box
              sx={{
                width: '20px',
                height: '20px',
                backgroundColor: '#333234',
                borderRadius: '50%',
                marginRight: '0.5rem'
              }}
            />
            <Box
              sx={{
                width: '60px',
                height: '16px',
                backgroundColor: '#333234',
                borderRadius: '4px'
              }}
            />
          </Box>

          {/* Amount skeleton */}
          <Box
            className='winnerAmount'
            display='flex'
            justifyContent='center'
            alignItems='center'
          >
            <Box
              sx={{
                width: '20px',
                height: '20px',
                backgroundColor: '#333234',
                borderRadius: '50%',
                marginRight: '0.5rem'
              }}
            />
            <Box
              sx={{
                width: '50px',
                height: '18px',
                backgroundColor: '#333234',
                borderRadius: '4px'
              }}
            />
          </Box>
        </Grid>
      </Box>
    </Box>
  ))

  return (
    <section>
      <LatestWinnerWrapper theme={theme}>
        <Grid className='gameHeading'>
          <Grid className='heading'>
            <figure>
              <img src={TrophyIcon} alt="Trophy" width='28px' height='28px' loading="eager" />
            </figure>
            <Typography>Live Winners</Typography>
          </Grid>
        </Grid>
        <Box className='winners-wrap'>
          {skeletonItems}
        </Box>
      </LatestWinnerWrapper>
    </section>
  )
}

export default LiveWinnersSkeleton
