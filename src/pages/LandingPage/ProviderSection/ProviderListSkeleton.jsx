import React from 'react'
import { Grid, Box, useTheme } from '@mui/material'
import { GameSwiperWrapper } from '../../GameSlider/gameslider.styles'
import useStyles from './style'

const ProviderListSkeleton = () => {
  const theme = useTheme()
  const classes = useStyles()

  // Create skeleton provider cards
  const skeletonProviders = Array.from({ length: 7 }, (_, index) => (
    <Box
      key={`skeleton-provider-${index}`}
      sx={{
        width: '100%',
        aspectRatio: '5/3', // Provider logo aspect ratio
        backgroundColor: '#333234',
        borderRadius: '8px',
        position: 'relative',
        overflow: 'hidden',
        marginRight: '10px',
        minWidth: {
          xs: 'calc(33.333% - 7px)', // 3 per row on mobile
          sm: 'calc(25% - 8px)',     // 4 per row on tablet
          md: 'calc(25% - 8px)',     // 4 per row on medium
          lg: 'calc(14.28% - 9px)'   // 7 per row on desktop
        }
      }}
    >
      {/* Shimmer effect */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `
            linear-gradient(
              110deg,
              transparent 0%,
              transparent 40%,
              rgba(255, 255, 255, 0.08) 50%,
              rgba(255, 255, 255, 0.12) 55%,
              rgba(255, 255, 255, 0.08) 60%,
              transparent 70%,
              transparent 100%
            )
          `,
          backgroundSize: '200% 100%',
          animation: 'shimmer 2s ease-in-out infinite',
          '@keyframes shimmer': {
            '0%': {
              backgroundPosition: '-200% 0'
            },
            '100%': {
              backgroundPosition: '200% 0'
            }
          }
        }}
      />

      {/* Logo placeholder */}
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '60%',
          height: '40%',
          backgroundColor: '#444',
          borderRadius: '4px'
        }}
      />
    </Box>
  ))

  return (
    <GameSwiperWrapper theme={theme}>
      <section className='provider-section'>
        <Grid className='provider-card-wrap'>
          <Box
            sx={{
              display: 'flex',
              gap: '10px',
              overflowX: 'hidden',
              padding: '10px 0'
            }}
          >
            {skeletonProviders}
          </Box>
        </Grid>
      </section>
    </GameSwiperWrapper>
  )
}

export default ProviderListSkeleton
