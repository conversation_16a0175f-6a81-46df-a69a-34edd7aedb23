import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  LobbyProviderSection: {
    //padding: theme.spacing(3, 0),
    margin: theme.spacing(2, 0, 1),
    [theme.breakpoints.down('sm')]: {
      margin: theme.spacing(0.625, 0)
    },
    position: 'relative',
    '& .heading': {
      color: theme.colors.yellow,
      fontSize: theme.spacing(1.5),
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(2)
      },
      marginBottom: '20px'
    },
    '& .provider-section': {
      padding: theme.spacing(1.2, 3),
      borderRadius: theme.spacing(1),
      // border : '1px solid #958484',
      background: theme.colors.textBlack,
      overflow: 'hidden', // Prevent flash
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(0.625, 1.5)
      },
      '& .swiper-wrapper': {
        marginTop: '0',
        '& .swiper-slide': {
          width: 'auto !important',
          flexShrink: 0,
          '& .custom-col-2': {
            width: '100%',
            height: '100%'
          }
        }
      },
      '& .swiper-container': {
        overflow: 'hidden'
      },
      '& .mySwiper': {
        '&:not(.swiper-initialized)': {
          '& .swiper-slide': {
            width: `${100/3}% !important`, // Default mobile width
            [theme.breakpoints.up('md')]: {
              width: `${100/4}% !important` // Default tablet width
            },
            [theme.breakpoints.up('lg')]: {
              width: `${100/7}% !important` // Default desktop width
            }
          }
        }
      },
      '& .provider-card-wrap': {
        '& .provider-card': {
          borderRadius: theme.spacing(0.625),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: theme.colors.sidebarBg,
          padding: theme.spacing(1),
          width: '100%',
          minHeight: theme.spacing(6.6875),
          maxHeight: theme.spacing(6.6875), // Prevent expansion
          flex: '0 0 auto',
          cursor: 'pointer',
          border: '1px solid transparent',
          overflow: 'hidden', // Prevent image overflow
          [theme.breakpoints.down('md')]: {
            minHeight: theme.spacing(3.75),
            padding: theme.spacing(0.625),
            borderRadius: theme.spacing(0.313)
          },
          [theme.breakpoints.down('sm')]: {
            // minHeight: theme.spacing(3),
            padding: theme.spacing(0.25)
          },
          '&:hover': {
            // borderColor: `${theme.colors.YellowishOrange}`,
            [theme.breakpoints.down('md')]: {
              borderColor: 'transparent'
            }
          },
          '& img': {
            width: '80% !important',
            height: 'auto !important',
            maxWidth: '140px !important',
            maxHeight: '100% !important',
            objectFit: 'contain !important',
            display: 'block !important',
            transition: 'none !important', // Prevent flash
            [theme.breakpoints.down(991)]: {
              width: '100% !important',
              maxWidth: '105px !important'
            },
            [theme.breakpoints.down('sm')]: {
              width: '100% !important',
              maxWidth: '80px !important'
            }
          }
        },
        '& .selected-provider-card': {
          borderRadius: theme.spacing(0.625),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: theme.colors.sidebarBg,
          padding: theme.spacing(1),
          width: '100%',
          minHeight: theme.spacing(6.6875),
          maxHeight: theme.spacing(6.6875), // Prevent expansion
          flex: '0 0 auto',
          cursor: 'pointer',
          border: '1px solid transparent',
          borderColor: `${theme.colors.YellowishOrange}`,
          overflow: 'hidden', // Prevent image overflow
          [theme.breakpoints.down('md')]: {
            minHeight: theme.spacing(3.75),
            padding: theme.spacing(0.625),
            borderRadius: theme.spacing(0.313)
          },
          // [theme.breakpoints.down('sm')]: {
          //   minHeight: theme.spacing(2.5)
          // },
          '& img': {
            width: '80%',
            maxWidth: '140px !important',
            maxHeight: '100%',
            objectFit: 'contain',
            display: 'block',
            transition: 'none', // Prevent flash
            [theme.breakpoints.down(991)]: {
              width: '100% !important',
              maxWidth: '105px !important'
            },
            [theme.breakpoints.down('sm')]: {
              width: '100% !important',
              maxWidth: '80px !important'
            }

          }
        }
      }
    },
    '& .button-next, & .button-prev': {
      border: 'none',
      zIndex: '1',
      padding: theme.spacing(0.2),
      background: 'transparent',
      position: 'absolute',
      left: theme.spacing(1.5),
      top: theme.spacing(4.75),
      transform: 'translate(-50%, -50%)',
      cursor: 'pointer',
      [theme.breakpoints.down('md')]: {
        top: theme.spacing(3.2)
      },
      [theme.breakpoints.down('sm')]: {
        top: theme.spacing(2.75)
      },
      '& img': {
        width: '12px',
        position: 'static',
        [theme.breakpoints.down('md')]: {
          width: '10px'
          // top: "35px",
        },
        [theme.breakpoints.down('sm')]: {
          width: '8px'
        }
      }
    },
    '&  .button-next': {
      right: theme.spacing(0),
      left: 'auto',
      [theme.breakpoints.down('md')]: {
        right: '-1px'
      }
    },
    '& .button-prev': {
      [theme.breakpoints.down('md')]: {
        left: theme.spacing(0.875)
      }
    }
  }
}))
