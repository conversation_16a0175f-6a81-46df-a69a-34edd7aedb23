import React from 'react'
import { Grid, Box, useTheme } from '@mui/material'
import useStyles from '../Lobby/Lobby.styles'

const LobbyFilterSkeleton = () => {
  const classes = useStyles()
  const theme = useTheme()

  // Create skeleton tabs
  const skeletonTabs = Array.from({ length: 6 }, (_, index) => (
    <Box
      key={`skeleton-tab-${index}`}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '8px',
        padding: '12px 16px',
        minWidth: '80px',
        backgroundColor: '#333234',
        borderRadius: '8px',
        marginRight: '8px',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Icon skeleton */}
      <Box
        sx={{
          width: '24px',
          height: '24px',
          backgroundColor: '#444',
          borderRadius: '4px'
        }}
      />
      
      {/* Text skeleton */}
      <Box
        sx={{
          width: index === 0 ? '40px' : index === 1 ? '60px' : '50px',
          height: '12px',
          backgroundColor: '#444',
          borderRadius: '6px'
        }}
      />

      {/* Shimmer effect */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `
            linear-gradient(
              110deg,
              transparent 0%,
              transparent 40%,
              rgba(255, 255, 255, 0.08) 50%,
              rgba(255, 255, 255, 0.12) 55%,
              rgba(255, 255, 255, 0.08) 60%,
              transparent 70%,
              transparent 100%
            )
          `,
          backgroundSize: '200% 100%',
          animation: 'shimmer 2s ease-in-out infinite',
          '@keyframes shimmer': {
            '0%': {
              backgroundPosition: '-200% 0'
            },
            '100%': {
              backgroundPosition: '200% 0'
            }
          }
        }}
      />
    </Box>
  ))

  return (
    <section className={classes.lobbyFilterWrap}>
      <Grid container spacing={1}>
        <Grid item xs={12} lg={12}>
          <Grid className={classes.lobbyFilterSection}>
            <Box className='lobby-filter-left'>
              <Grid className={classes.roundedThemeTabs}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    overflowX: 'auto',
                    scrollbarWidth: 'none',
                    '&::-webkit-scrollbar': {
                      display: 'none'
                    },
                    padding: '8px 0'
                  }}
                >
                  {skeletonTabs}
                </Box>
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </section>
  )
}

export default LobbyFilterSkeleton
