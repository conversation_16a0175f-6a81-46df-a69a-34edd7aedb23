import { create } from 'zustand'

import { createDebugStore } from './middleware/devtools'
import { registerStoreForInspection } from './utils/storeInspector'

const initialState = {
  jackpotOn: false,
  jackpotMultiplier: 1,
  jackpotData: {
    jackpotPoolAmount: 0,
    entryAmount: 0,
    recentJackpotWinners: []
  },
  jackpotWin: false,
  jackpotWinAmount: 0,
  newJackpot: false
}

const jackpotStoreConfig = (set, get) => ({
  ...initialState,

  // Setters
  setJackpotOn: (data) => {
    set(() => ({ jackpotOn: data }))
  },
  setJackpotData: (key, value) =>
    set((state) => ({
      jackpotData: {
        ...state.jackpotData,
        [key]: value
      }
    })),
  setJackpotPoolAmount: (amount) =>
    set((state) => ({
      jackpotData: {
        ...state.jackpotData,
        jackpotPoolAmount: amount
      }
    })),
  setJackpotWin: (status, data) => {
    set(() => ({ jackpotWin: status, jackpotWinAmount: data }))
  },
  setNewJackpot: (data) => {
    set(() => ({ newJackpot: data }))
  },
  setJackpotMultiplier: (data) => {
    set(() => ({ jackpotMultiplier: data }))
  },
  // Reset
  resetJackpotStore: () => {
    const { jackpotData } = get()
    set(() => ({
      ...initialState,
      jackpotData: {
        ...initialState.jackpotData,
        jackpotPoolAmount: jackpotData.jackpotPoolAmount // preserve this
      }
    }))
  }
})

export const useJackpotStore = create(
  createDebugStore('JackpotStore', jackpotStoreConfig, {
    devtoolsOptions: {
      name: 'Jackpot Store'
    }
  })
)

// Register store for inspection in development
if (import.meta.env.DEV) {
  registerStoreForInspection('JackpotStore', useJackpotStore)
}
