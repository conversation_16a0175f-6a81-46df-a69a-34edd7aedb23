import { devtools } from 'zustand/middleware'

/**
 * Enhanced devtools middleware with custom configuration
 * Provides Redux DevTools integration for Zustand stores
 */
export const createDevtoolsMiddleware = (name, options = {}) => {
  if (!import.meta.env.DEV) {
    // Return identity function in production
    return (config) => config
  }

  const defaultOptions = {
    enabled: true,
    name: name,
    serialize: {
      options: {
        undefined: true,
        function: true,
        symbol: true,
      },
    },
    ...options,
  }

  return devtools(defaultOptions)
}

/**
 * Logger middleware for console debugging
 * Logs all state changes with timestamps and diff
 */
export const logger = (config) => (set, get, api) => {
  if (!import.meta.env.DEV) {
    return config(set, get, api)
  }

  return config(
    (...args) => {
      const prevState = get()
      set(...args)
      const nextState = get()

      // console.group(`🏪 Store Update: ${api.devtools?.name || 'Unknown Store'}`)
      console.log('⏰ Timestamp:', new Date().toISOString())
      console.log('📤 Previous State:', prevState)
      console.log('📥 Next State:', nextState)
      console.log('🔄 State Diff:', getStateDiff(prevState, nextState))
      console.groupEnd()
    },
    get,
    api
  )
}





/**
 * Action tracking middleware
 * Tracks which actions are called and their frequency
 */
export const actionTracker = (config) => (set, get, api) => {
  const actionCounts = new Map()
  
  return config(
    (...args) => {
      if (import.meta.env.DEV) {
        const action = args[0]?.name || 'anonymous'
        actionCounts.set(action, (actionCounts.get(action) || 0) + 1)
        
        // Log action frequency every 10 calls
        const totalCalls = Array.from(actionCounts.values()).reduce((a, b) => a + b, 0)
        if (totalCalls % 10 === 0) {
          console.table(Object.fromEntries(actionCounts))
        }
      }
      
      set(...args)
    },
    get,
    api
  )
}

/**
 * Utility function to calculate state differences
 */
function getStateDiff(prev, next) {
  const diff = {}
  
  for (const key in next) {
    if (prev[key] !== next[key]) {
      diff[key] = {
        from: prev[key],
        to: next[key]
      }
    }
  }
  
  return Object.keys(diff).length > 0 ? diff : 'No changes'
}



/**
 * Simple and reliable debug store creator
 * Uses only the standard Zustand devtools middleware
 */
export const createDebugStore = (name, storeConfig, options = {}) => {
  if (!import.meta.env.DEV) {
    // In production, just return the basic store
    return storeConfig
  }

  const {
    enableLogger = false,
    devtoolsOptions = {}
  } = options

  // Apply devtools middleware
  let config = devtools(storeConfig, {
    name: devtoolsOptions.name || name,
    enabled: true,
    ...devtoolsOptions
  })

  // If logger is enabled, we'll set it up after store creation
  if (enableLogger) {
    const originalConfig = config
    config = (set, get, api) => {
      const store = originalConfig(set, get, api)

      // Add logging to all setter methods
      Object.keys(store).forEach(key => {
        if (typeof store[key] === 'function' && key.startsWith('set')) {
          const originalMethod = store[key]
          store[key] = (...args) => {
            // console.log(`🏪 ${name}.${key}() called with:`, args)
            const result = originalMethod(...args)
            // console.log(`🏪 ${name} new state:`, get())
            return result
          }
        }
      })

      return store
    }
  }

  return config
}
