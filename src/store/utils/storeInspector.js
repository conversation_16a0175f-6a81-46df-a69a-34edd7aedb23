/**
 * Zustand Store Inspector Utility
 * Provides runtime debugging capabilities for Zustand stores
 */

class StoreInspector {
  constructor() {
    this.stores = new Map()
    this.subscribers = new Map()
    this.history = []
    this.maxHistorySize = 100
    
    // Make inspector available globally in development
    if (import.meta.env.DEV) {
      window.zustandInspector = this
    }
  }

  /**
   * Register a store for inspection
   */
  registerStore(name, store) {
    this.stores.set(name, store)
    
    // Subscribe to store changes
    const unsubscribe = store.subscribe((state, prevState) => {
      this.recordStateChange(name, prevState, state)
    })
    
    this.subscribers.set(name, unsubscribe)
    
    // console.log(`🔍 Registered store for inspection: ${name}`)
  }

  /**
   * Unregister a store
   */
  unregisterStore(name) {
    const unsubscribe = this.subscribers.get(name)
    if (unsubscribe) {
      unsubscribe()
      this.subscribers.delete(name)
    }
    this.stores.delete(name)
  }

  /**
   * Record state changes in history
   */
  recordStateChange(storeName, prevState, nextState) {
    const timestamp = new Date().toISOString()
    const change = {
      timestamp,
      storeName,
      prevState: JSON.parse(JSON.stringify(prevState)),
      nextState: JSON.parse(JSON.stringify(nextState)),
      diff: this.calculateDiff(prevState, nextState)
    }

    this.history.unshift(change)
    
    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(0, this.maxHistorySize)
    }
  }

  /**
   * Calculate differences between states
   */
  calculateDiff(prev, next) {
    const diff = {}
    
    // Check for changed/added properties
    for (const key in next) {
      if (prev[key] !== next[key]) {
        diff[key] = {
          type: key in prev ? 'changed' : 'added',
          from: prev[key],
          to: next[key]
        }
      }
    }
    
    // Check for removed properties
    for (const key in prev) {
      if (!(key in next)) {
        diff[key] = {
          type: 'removed',
          from: prev[key],
          to: undefined
        }
      }
    }
    
    return diff
  }

  /**
   * Get current state of all stores
   */
  getAllStates() {
    const states = {}
    for (const [name, store] of this.stores) {
      states[name] = store.getState()
    }
    return states
  }

  /**
   * Get current state of a specific store
   */
  getStoreState(name) {
    const store = this.stores.get(name)
    return store ? store.getState() : null
  }

  /**
   * Get store names
   */
  getStoreNames() {
    return Array.from(this.stores.keys())
  }

  /**
   * Get change history
   */
  getHistory(storeName = null, limit = 10) {
    let history = this.history
    
    if (storeName) {
      history = history.filter(change => change.storeName === storeName)
    }
    
    return history.slice(0, limit)
  }

  /**
   * Clear history
   */
  clearHistory() {
    this.history = []
    console.log('🧹 Store history cleared')
  }

  /**
   * Print store summary
   */
  printSummary() {
    console.group('🏪 Zustand Store Inspector Summary')
    console.log('📊 Registered Stores:', this.getStoreNames())
    console.log('📈 History Entries:', this.history.length)
    console.log('🕐 Last Update:', this.history[0]?.timestamp || 'No updates')
    
    // Print current states
    const states = this.getAllStates()
    for (const [name, state] of Object.entries(states)) {
      console.group(`📦 ${name}`)
      console.log(state)
      console.groupEnd()
    }
    
    console.groupEnd()
  }



  /**
   * Watch for specific state changes
   */
  watchState(storeName, path, callback) {
    const store = this.stores.get(storeName)
    if (!store) {
      console.warn(`Store ${storeName} not found`)
      return
    }

    return store.subscribe((state) => {
      const value = this.getNestedValue(state, path)
      callback(value, state)
    })
  }

  /**
   * Get nested value from object using dot notation
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * Time travel debugging - restore previous state
   */
  timeTravel(historyIndex) {
    if (historyIndex >= this.history.length) {
      console.warn('Invalid history index')
      return
    }

    const change = this.history[historyIndex]
    const store = this.stores.get(change.storeName)
    
    if (store && store.setState) {
      store.setState(change.prevState)
      console.log(`⏰ Time traveled to: ${change.timestamp}`)
    }
  }
}

// Create global inspector instance
export const storeInspector = new StoreInspector()

/**
 * Helper function to register store with inspector
 */
export const registerStoreForInspection = (name, store) => {
  if (import.meta.env.DEV) {
    storeInspector.registerStore(name, store)
  }
}

/**
 * Console commands for debugging (available in dev mode)
 */
if (import.meta.env.DEV) {
  window.zustandDebug = {
    // Quick access methods
    states: () => storeInspector.getAllStates(),
    history: (store, limit) => storeInspector.getHistory(store, limit),
    summary: () => storeInspector.printSummary(),
    clear: () => storeInspector.clearHistory(),
    watch: (store, path, callback) => storeInspector.watchState(store, path, callback),
    timeTravel: (index) => storeInspector.timeTravel(index),
    
    // Help command
    help: () => {
      console.log(`
🔍 Zustand Debug Commands:
- zustandDebug.states() - Get all store states
- zustandDebug.history(storeName?, limit?) - Get change history
- zustandDebug.summary() - Print stores summary
- zustandDebug.clear() - Clear history
- zustandDebug.watch(storeName, path, callback) - Watch state changes
- zustandDebug.timeTravel(index) - Restore previous state
- zustandDebug.help() - Show this help
      `)
    }
  }
  
  console.log('🔍 Zustand debugging tools loaded! Type zustandDebug.help() for commands')
}
