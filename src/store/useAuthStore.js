import { create } from 'zustand'

import { deleteCookie, getCookie, setCookie } from '../utils/cookiesCollection'
import { createDebugStore } from './middleware/devtools'
import { registerStoreForInspection } from './utils/storeInspector'

// Cache the initial cookie check to avoid repeated DOM queries
const initialPathCookieCheck = (() => {
  try {
    return getCookie('path') ? true : false
  } catch (error) {
    console.warn('Cookie check failed:', error)
    return false
  }
})()

const authStoreConfig = (set) => ({
  isAuthenticated: false,
  pathCookieCheck: initialPathCookieCheck,
  setAuthenticated: (value) => {
    set({ isAuthenticated: value })
  },
  setPathCookieCheck: (value) => {
    if (value) {
      setCookie('path', '/home', 30)
    } else {
      deleteCookie('path')
    }
    set({ pathCookieCheck: value })
  }
})

const useAuthStore = create(
  createDebugStore('AuthStore', authStoreConfig, {
    devtoolsOptions: {
      name: 'Auth Store'
    }
  })
)

// Register store for inspection in development
if (import.meta.env.DEV) {
  registerStoreForInspection('AuthStore', useAuthStore)
}

export default useAuthStore
