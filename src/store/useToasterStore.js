import {create} from 'zustand';

import { createDebugStore } from './middleware/devtools'
import { registerStoreForInspection } from './utils/storeInspector'

const toasterStoreConfig = (set) => ({
    openToaster:false,
    toasterMessage :'',
    toasterType:'success',

    handleToaster: ({ openToaster, toasterMessage, toasterType }) => set({
        openToaster,
        toasterMessage,
        toasterType,
      }),
    clearToaster : ()=>set({
      openToaster:false,
      toasterMessage :'',
      toasterType:'success',}),

})

const useToasterStore = create(
  createDebugStore('ToasterStore', toasterStoreConfig, {
    devtoolsOptions: {
      name: 'Toaster Store'
    }
  })
)

// Register store for inspection in development
if (import.meta.env.DEV) {
  registerStoreForInspection('ToasterStore', useToasterStore)
}

export default useToasterStore;