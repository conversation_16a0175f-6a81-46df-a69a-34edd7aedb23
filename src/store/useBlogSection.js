// store/blogStore.js
import { create } from 'zustand';

import { createDebugStore } from './middleware/devtools'
import { registerStoreForInspection } from './utils/storeInspector'

const blogStoreConfig = (set, get) => ({
  blogs: [],
  selectedBlog: null,

  setBlogs: (blogs) => set({ blogs }),

  setSelectedBlog: (blog) => set({ selectedBlog: blog }),

  // Get blog by id or slug from current blogs
  getBlogByIdOrSlug: (idOrSlug) => {
    const blogs = get().blogs;
    return blogs.find((blog) => blog.id === idOrSlug || blog.slug === idOrSlug) || null;
  },
})

const useBlogStore = create(
  createDebugStore('BlogStore', blogStoreConfig, {
    devtoolsOptions: {
      name: 'Blog Store'
    }
  })
)

// Register store for inspection in development
if (import.meta.env.DEV) {
  registerStoreForInspection('BlogStore', useBlogStore)
}

export default useBlogStore;
