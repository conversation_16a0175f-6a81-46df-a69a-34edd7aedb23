import { create } from 'zustand'

import { createDebugStore } from './middleware/devtools'
import { registerStoreForInspection } from './utils/storeInspector'

const initialState = {
  tokens: {
    singleUseCustomerToken: '',
    paymentHandleToken: '',
    transactionId: ''
  },
  data: {
    isPaymentScreenLoading: false,
    depositInitiated: false,
    depositInitFailed: false,
    packageData: null,
    finalAmount: null,
    discountAmount: null,
    paymentData: null,
    demoInstance: null,
    activePaymentMethods: [],
    selectedPaymentMethod: null,
    preferredPayments: null,
    selectedPreferredPayment: null,
    selectedPreferredPaymentId: ''
  },
  status: {
    value: 'payment',
    isLoading: false,
    confettiPopper: null,
    isPaymentScreenLoading: false,
    depositInitiated: false,
    depositInitFailed: {},
    paymentRedirection: false,
    paymentDisabled: true,
    showAllPayments: false
  },
  card: {
    cardHolderName: '',
    savePaymentToggle: false,
    selectedSavedCard: '',
    cardErrors: {
      cardHolderName: '',
      cardNumber: '',
      expiryDate: '',
      cvv: ''
    },
    cardBrandRecognition: ''
  },
  promocode: {
    promocodeModalOpen: false,
    selectedPromocode: '',
    promocodeSuccess: false,
    promocode: '',
    isPromocodeLoading: false
  },
  trustly: {
    showTrustlyStatus: false,
    isTrustlyWidgetLoading: false,
    isTrustlyCheckboxSelected: false,
    trustlyTokenExpiredScriptLoaded: null,
    isPreferredHasTrustly: false
  }
}

const paymentStoreConfig = (set, get) => ({
  ...initialState,

  // Setters
  setPaymentToken: (key, value) =>
    set((state) => ({
      tokens: {
        ...state.tokens,
        [key]: value
      }
    })),

  setPaymentData: (key, value) =>
    set((state) => ({
      data: {
        ...state.data,
        [key]: value
      }
    })),

  setPaymentStatus: (key, value) =>
    set((state) => ({
      status: {
        ...state.status,
        [key]: value
      }
    })),

  setPaymentCard: (key, value) =>
    set((state) => ({
      card: {
        ...state.card,
        [key]: value
      }
    })),

  setCardErrors: (field, error) =>
    set((state) => ({
      card: {
        ...state.card,
        cardErrors: {
          ...state.card.cardErrors,
          [field]: error
        }
      }
    })),

  setPaymentPromocode: (key, value) =>
    set((state) => ({
      promocode: {
        ...state.promocode,
        [key]: value
      }
    })),

  setPaymentTrustly: (key, value) =>
    set((state) => ({
      trustly: {
        ...state.trustly,
        [key]: value
      }
    })),
  // Getters
  getToken: (key) => get().tokens[key],
  getAllTokens: () => get().tokens,

  getData: (key) => get().data[key],
  getAllData: () => get().data,

  getStatus: (key) => get().status[key],
  getAllStatus: () => get().status,

  getCard: (key) => get().card[key],
  getAllCard: () => get().card,

  // Reset
  resetPaymentStore: () => set(initialState)
})

export const usePaymentStore = create(
  createDebugStore('PaymentStore', paymentStoreConfig, {
    devtoolsOptions: {
      name: 'Payment Store'
    }
  })
)

// Register store for inspection in development
if (import.meta.env.DEV) {
  registerStoreForInspection('PaymentStore', usePaymentStore)
}
